import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { authenticate } from '@/lib/auth';
import { auditEvents } from '@/lib/audit';
import { rateLimit } from '@/middleware/auth';

// Input validation schema
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required')
});

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = rateLimit(request);
    if (rateLimitResponse) {
      return rateLimitResponse;
    }
    
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    // Parse and validate request body
    const body = await request.json();
    const validatedData = loginSchema.parse(body);
    
    // Attempt authentication
    const authResult = await authenticate(validatedData.email, validatedData.password);
    
    if (!authResult.success) {
      // Log failed login attempt
      await auditEvents.loginFailed(validatedData.email, clientIP, userAgent);
      
      return NextResponse.json(
        { 
          error: 'Authentication failed',
          message: 'Invalid email or password'
        },
        { status: 401 }
      );
    }
    
    // Log successful login
    await auditEvents.loginSuccess(authResult.user!.id, clientIP, userAgent);
    
    // Create response with secure cookies
    const response = NextResponse.json({
      success: true,
      user: {
        id: authResult.user!.id,
        email: authResult.user!.email,
        name: authResult.user!.name,
        role: authResult.user!.role,
        permissions: authResult.user!.permissions
      }
    });
    
    // Set secure HTTP-only cookies
    response.cookies.set('auth-token', authResult.token!, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/'
    });
    
    response.cookies.set('refresh-token', authResult.refreshToken!, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/'
    });
    
    return response;
    
  } catch (error) {
    console.error('Login API error:', error);
    
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    // Log the error
    await auditEvents.suspiciousActivity(
      'Login API error',
      clientIP,
      userAgent,
      { error: error instanceof Error ? error.message : 'Unknown error' }
    );
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Validation Error',
          message: 'Invalid input data',
          details: error.errors
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        error: 'Internal Server Error',
        message: 'Login failed'
      },
      { status: 500 }
    );
  }
}
