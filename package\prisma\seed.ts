import { PrismaClient } from '@prisma/client'
import { mockTeachers, mockStudents, mockCabinets, mockClasses, mockPayments } from '../src/mock/data'

const prisma = new PrismaClient()

async function main() {
  // Clear existing data
  await prisma.payment.deleteMany()
  await prisma.class.deleteMany()
  await prisma.student.deleteMany()
  await prisma.teacher.deleteMany()
  await prisma.cabinet.deleteMany()

  // Create teachers
  const teachers = await Promise.all(
    mockTeachers.map(teacher => 
      prisma.teacher.create({
        data: {
          name: teacher.name,
          email: teacher.email,
          phone: teacher.phone,
          subjects: teacher.subjects,
          qualifications: teacher.qualifications,
          joinDate: new Date(teacher.joinDate),
          status: teacher.status,
        },
      })
    )
  )

  // Create cabinets
  const cabinets = await Promise.all(
    mockCabinets.map(cabinet =>
      prisma.cabinet.create({
        data: {
          name: cabinet.name,
          capacity: cabinet.capacity,
          equipment: cabinet.equipment,
          status: cabinet.status,
          location: cabinet.location,
        },
      })
    )
  )

  // Create students
  const students = await Promise.all(
    mockStudents.map(student =>
      prisma.student.create({
        data: {
          name: student.name,
          phone: student.phone,
          joinDate: new Date(student.joinDate),
          paymentStatus: student.paymentStatus,
        },
      })
    )
  )

  // Create classes
  const classes = await Promise.all(
    mockClasses.map(async (cls) => {
      const teacher = teachers.find(t => t.name === cls.teacher)
      const cabinet = cabinets.find(c => c.id === cls.cabinetId)
      
      if (!teacher || !cabinet) throw new Error('Teacher or cabinet not found')

      return prisma.class.create({
        data: {
          name: cls.name,
          teacherId: teacher.id,
          subject: cls.subject,
          level: cls.level,
          cabinetId: cabinet.id,
          schedule: cls.schedule,
          courseAmount: cls.courseAmount,
          createdAt: new Date(cls.createdAt),
          openingDate: cls.openingDate ? new Date(cls.openingDate) : null,
          students: {
            connect: cls.students.map(studentId => ({
              id: students[parseInt(studentId) - 1].id
            }))
          }
        },
      })
    })
  )

  // Create payments
  await Promise.all(
    mockPayments.map(payment => {
      const student = students[parseInt(payment.studentId) - 1]
      if (!student) throw new Error('Student not found')

      return prisma.payment.create({
        data: {
          studentId: student.id,
          amount: payment.amount,
          date: new Date(payment.date),
          status: payment.status,
          method: payment.method,
          description: payment.description,
        },
      })
    })
  )

  console.log('Database seeded!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })