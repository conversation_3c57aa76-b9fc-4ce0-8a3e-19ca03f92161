'use client';
import { useRouter } from 'next/navigation';
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Avatar,
  Snackbar,
  Alert,
  TextField,
  InputAdornment,
  Pagination,
  Stack,
} from '@mui/material';
import { IconSearch } from '@tabler/icons-react';
import PageContainer from '@/app/(DashboardLayout)/components/container/PageContainer';
import { IconEdit, IconTrash } from '@tabler/icons-react';
import { useState, useEffect, useMemo } from 'react';
import { usePayments } from '@/hooks/usePayments';
import StudentDialog from './StudentDialog';
import type { StudentWithClasses } from '@/types';

interface StudentFormData {
  name: string;
  phone: string;
  joinDate?: Date;
  paymentStatus?: string;
  selectedClassIds?: string[];
}

const Students = () => {
  const router = useRouter();
  const [students, setStudents] = useState<StudentWithClasses[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<StudentWithClasses | undefined>();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [rowsPerPage] = useState(20);

  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  const fetchStudents = async () => {
    try {
      const response = await fetch('/api/students');
      if (!response.ok) throw new Error('Failed to fetch students');
      const data = await response.json();
      setStudents(data);
    } catch (error) {
      showNotification('Failed to fetch students', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStudents();
  }, [fetchStudents]);

  const { getStudentPaymentStatus } = usePayments();
  
  const getPaymentStatusColor = (status: string) => {
    return status === 'paid' ? 'success' : 'error';
  };

  const getStatusDisplayText = (status: string) => {
    return status === 'paid' ? 'Оплатил' : 'Не оплатил';
  };

  // Memoize payment status for each student
  const studentPaymentStatuses = useMemo(() => {
    return students.reduce((acc, student) => {
      acc[student.id] = getStudentPaymentStatus(student.id);
      return acc;
    }, {} as Record<string, string>);
  }, [students, getStudentPaymentStatus]);

  // Memoize filtered students for performance
  const filteredStudents = useMemo(() => {
    return students.filter((student) =>
      student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.classes.some(cls =>
        cls.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    );
  }, [students, searchQuery]);

  // Reset to first page when search query changes
  useEffect(() => {
    setPage(1);
  }, [searchQuery]);

  const handleAddStudent = () => {
    setSelectedStudent(undefined);
    setDialogOpen(true);
  };

  const handleEditStudent = (student: StudentWithClasses) => {
    setSelectedStudent(student);
    setDialogOpen(true);
  };

  const handleDeleteStudent = async (studentId: string) => {
    try {
      const response = await fetch(`/api/students?id=${studentId}`, {
        method: 'DELETE',
      });
      if (!response.ok) throw new Error('Failed to delete student');
      
      setStudents((prevStudents) =>
        prevStudents.filter((student) => student.id !== studentId)
      );
      showNotification('Student deleted successfully', 'success');
    } catch (error) {
      showNotification('Failed to delete student', 'error');
    }
  };

  const handleSaveStudent = async (formData: StudentFormData) => {
    try {
      const method = selectedStudent ? 'PUT' : 'POST';
      const body = {
        ...formData,
        ...(selectedStudent && { id: selectedStudent.id }),
        classIds: formData.selectedClassIds,
      };

      const response = await fetch('/api/students', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });

      if (!response.ok) throw new Error(`Failed to ${selectedStudent ? 'update' : 'create'} student`);
      
      const savedStudent = await response.json();
      
      if (selectedStudent) {
        setStudents((prevStudents) =>
          prevStudents.map((student) =>
            student.id === selectedStudent.id ? savedStudent : student
          )
        );
        showNotification('Student updated successfully', 'success');
      } else {
        setStudents((prevStudents) => [...prevStudents, savedStudent]);
        showNotification('Student added successfully', 'success');
      }
      setDialogOpen(false);
    } catch (error) {
      showNotification(
        `Failed to ${selectedStudent ? 'update' : 'add'} student`,
        'error'
      );
    }
  };

  const showNotification = (message: string, severity: 'success' | 'error') => {
    setNotification({
      open: true,
      message,
      severity,
    });
  };

  const handleCloseNotification = () => {
    setNotification((prev) => ({ ...prev, open: false }));
  };

  const handleChangePage = (event: React.ChangeEvent<unknown>, newPage: number) => {
    setPage(newPage);
  };

  if (loading) {
    return (
      <PageContainer title="Students" description="Manage students">
        <Typography>Loading...</Typography>
      </PageContainer>
    );
  }

  return (
    <PageContainer title="Students" description="Manage students">
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h3">Students</Typography>
                <Button variant="contained" color="primary" onClick={handleAddStudent}>
                  Add New Student
                </Button>
              </Box>
              <Box mb={3}>
                <TextField
                  fullWidth
                  variant="outlined"
                  placeholder="Search by name, phone, or class..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <IconSearch size={20} />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>

              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Student</TableCell>
                      <TableCell>Contact</TableCell>
                      <TableCell>Join Date</TableCell>
                      <TableCell>Classes</TableCell>
                      <TableCell>Payment Status</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredStudents
                      .slice((page - 1) * rowsPerPage, page * rowsPerPage)
                      .map((student) => (
                        <TableRow key={student.id}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Avatar
                                sx={{
                                  width: 35,
                                  height: 35,
                                  mr: 2,
                                  background: 'primary.main',
                                }}
                              >
                                {student.name.charAt(0)}
                              </Avatar>
                              <Box>
                                <Typography variant="subtitle2" fontWeight={600}>
                                  {student.name}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>{student.phone}</TableCell>
                          <TableCell>{new Date(student.joinDate).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                              {student.classes.map((cls) => (
                                <Box
                                  key={cls.id}
                                  component="div"
                                  onClick={() => router.push(`/class-management/classes?classId=${cls.id}`)}
                                  sx={{
                                    cursor: 'pointer',
                                    '&:hover': {
                                      color: 'primary.main',
                                      textDecoration: 'underline',
                                      transform: 'translateY(-1px)',
                                      transition: 'all 0.2s ease-in-out'
                                    },
                                    '&:active': {
                                      transform: 'translateY(0)'
                                    }
                                  }}
                                >
                                  <Typography
                                    variant="body2"
                                    color="primary"
                                  >
                                    {cls.name}
                                  </Typography>
                                </Box>
                              ))}
                              {student.classes.length === 0 && (
                                <Typography
                                  variant="body2"
                                  color="textSecondary"
                                >
                                  Not Assigned
                                </Typography>
                              )}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getStatusDisplayText(studentPaymentStatuses[student.id])}
                              color={getPaymentStatusColor(studentPaymentStatuses[student.id]) as any}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <IconButton
                              color="primary"
                              size="small"
                              sx={{ mr: 1 }}
                              onClick={() => handleEditStudent(student)}
                            >
                              <IconEdit size={18} />
                            </IconButton>
                            <IconButton
                              color="error"
                              size="small"
                              onClick={() => handleDeleteStudent(student.id)}
                            >
                              <IconTrash size={18} />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
              
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3, mb: 2 }}>
                <Stack spacing={2}>
                  <Pagination
                    count={Math.ceil(filteredStudents.length / rowsPerPage)}
                    page={page}
                    onChange={handleChangePage}
                    color="primary"
                    showFirstButton
                    showLastButton
                  />
                </Stack>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <StudentDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onSave={handleSaveStudent}
        student={selectedStudent}
      />

      <Snackbar
        open={notification.open}
        autoHideDuration={4000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </PageContainer>
  );
};

export default Students;