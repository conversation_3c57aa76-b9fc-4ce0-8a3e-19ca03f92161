import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/teachers
export async function GET() {
  try {
    const teachers = await prisma.teacher.findMany({
      include: {
        classes: {
          include: {
            students: true,
          },
        },
      },
    });
    return NextResponse.json(teachers);
  } catch (error) {
    console.error('Error fetching teachers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch teachers' },
      { status: 500 }
    );
  }
}

// POST /api/teachers
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const teacher = await prisma.teacher.create({
      data: {
        name: body.name,
        email: body.email,
        phone: body.phone,
        subjects: body.subjects,
        qualifications: body.qualifications,
        joinDate: new Date(body.joinDate),
        status: body.status || 'active',
      },
    });
    return NextResponse.json(teacher);
  } catch (error) {
    console.error('Error creating teacher:', error);
    return NextResponse.json(
      { error: 'Failed to create teacher' },
      { status: 500 }
    );
  }
}