"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CInnovative-Centre-Admin%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CInnovative-Centre-Admin%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CInnovative-Centre-Admin%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CInnovative-Centre-Admin%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_Innovative_Centre_Admin_package_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/login/route.ts */ \"(rsc)/./src/app/api/auth/login/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\Innovative-Centre-Admin\\\\package\\\\src\\\\app\\\\api\\\\auth\\\\login\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_Innovative_Centre_Admin_package_src_app_api_auth_login_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CInnovative-Centre-Admin%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CInnovative-Centre-Admin%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/login/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/auth/login/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_audit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/audit */ \"(rsc)/./src/lib/audit.ts\");\n/* harmony import */ var _middleware_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/middleware/auth */ \"(rsc)/./src/middleware/auth.ts\");\n\n\n\n\n\n// Input validation schema\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().email(\"Invalid email format\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, \"Password is required\")\n});\nasync function POST(request) {\n    try {\n        // Apply rate limiting\n        const rateLimitResponse = (0,_middleware_auth__WEBPACK_IMPORTED_MODULE_4__.rateLimit)(request);\n        if (rateLimitResponse) {\n            return rateLimitResponse;\n        }\n        const clientIP = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n        const userAgent = request.headers.get(\"user-agent\") || \"unknown\";\n        // Parse and validate request body\n        const body = await request.json();\n        const validatedData = loginSchema.parse(body);\n        // Attempt authentication\n        const authResult = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authenticate)(validatedData.email, validatedData.password);\n        if (!authResult.success) {\n            // Log failed login attempt\n            await _lib_audit__WEBPACK_IMPORTED_MODULE_3__.auditEvents.loginFailed(validatedData.email, clientIP, userAgent);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authentication failed\",\n                message: \"Invalid email or password\"\n            }, {\n                status: 401\n            });\n        }\n        // Log successful login\n        await _lib_audit__WEBPACK_IMPORTED_MODULE_3__.auditEvents.loginSuccess(authResult.user.id, clientIP, userAgent);\n        // Create response with secure cookies\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: {\n                id: authResult.user.id,\n                email: authResult.user.email,\n                name: authResult.user.name,\n                role: authResult.user.role,\n                permissions: authResult.user.permissions\n            }\n        });\n        // Set secure HTTP-only cookies\n        response.cookies.set(\"auth-token\", authResult.token, {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"strict\",\n            maxAge: 24 * 60 * 60,\n            path: \"/\"\n        });\n        response.cookies.set(\"refresh-token\", authResult.refreshToken, {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"strict\",\n            maxAge: 7 * 24 * 60 * 60,\n            path: \"/\"\n        });\n        return response;\n    } catch (error) {\n        console.error(\"Login API error:\", error);\n        const clientIP = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n        const userAgent = request.headers.get(\"user-agent\") || \"unknown\";\n        // Log the error\n        await _lib_audit__WEBPACK_IMPORTED_MODULE_3__.auditEvents.suspiciousActivity(\"Login API error\", clientIP, userAgent, {\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_1__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Validation Error\",\n                message: \"Invalid input data\",\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal Server Error\",\n            message: \"Login failed\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/audit.ts":
/*!**************************!*\
  !*** ./src/lib/audit.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditLevel: () => (/* binding */ AuditLevel),\n/* harmony export */   auditEvents: () => (/* binding */ auditEvents),\n/* harmony export */   auditLog: () => (/* binding */ auditLog),\n/* harmony export */   detectSuspiciousActivity: () => (/* binding */ detectSuspiciousActivity),\n/* harmony export */   getAuditLogs: () => (/* binding */ getAuditLogs)\n/* harmony export */ });\nvar AuditLevel;\n(function(AuditLevel) {\n    AuditLevel[\"INFO\"] = \"info\";\n    AuditLevel[\"WARN\"] = \"warn\";\n    AuditLevel[\"ERROR\"] = \"error\";\n    AuditLevel[\"CRITICAL\"] = \"critical\";\n})(AuditLevel || (AuditLevel = {}));\n// Critical actions that should always be logged\nconst CRITICAL_ACTIONS = [\n    \"LOGIN_SUCCESS\",\n    \"LOGIN_FAILED\",\n    \"LOGOUT\",\n    \"PASSWORD_CHANGE\",\n    \"USER_CREATED\",\n    \"USER_DELETED\",\n    \"PERMISSION_CHANGE\",\n    \"DATA_EXPORT\",\n    \"SYSTEM_CONFIG_CHANGE\",\n    \"UNAUTHORIZED_ACCESS_ATTEMPT\",\n    \"RATE_LIMIT_EXCEEDED\",\n    \"SUSPICIOUS_ACTIVITY\"\n];\n// High-risk entities that require detailed logging\nconst HIGH_RISK_ENTITIES = [\n    \"USER\",\n    \"ADMIN\",\n    \"TEACHER\",\n    \"PAYMENT\",\n    \"SYSTEM_CONFIG\"\n];\n// Determine audit level based on action and entity\nfunction getAuditLevel(action, entity) {\n    if (CRITICAL_ACTIONS.includes(action)) {\n        return \"critical\";\n    }\n    if (HIGH_RISK_ENTITIES.includes(entity.toUpperCase())) {\n        return \"warn\";\n    }\n    if (action.includes(\"DELETE\") || action.includes(\"REMOVE\")) {\n        return \"warn\";\n    }\n    return \"info\";\n}\n// Sanitize sensitive data before logging\nfunction sanitizeData(data) {\n    if (!data || typeof data !== \"object\") {\n        return data;\n    }\n    const sensitiveFields = [\n        \"password\",\n        \"token\",\n        \"secret\",\n        \"key\",\n        \"hash\"\n    ];\n    const sanitized = {\n        ...data\n    };\n    for (const field of sensitiveFields){\n        if (field in sanitized) {\n            sanitized[field] = \"[REDACTED]\";\n        }\n    }\n    // Recursively sanitize nested objects\n    for(const key in sanitized){\n        if (typeof sanitized[key] === \"object\" && sanitized[key] !== null) {\n            sanitized[key] = sanitizeData(sanitized[key]);\n        }\n    }\n    return sanitized;\n}\n// Main audit logging function\nasync function auditLog(entry) {\n    try {\n        const auditEnabled = process.env.AUDIT_LOG_ENABLED === \"true\";\n        if (!auditEnabled) {\n            return;\n        }\n        const level = getAuditLevel(entry.action, entry.entity);\n        const timestamp = new Date();\n        // Sanitize sensitive data\n        const sanitizedOldData = entry.oldData ? sanitizeData(entry.oldData) : null;\n        const sanitizedNewData = entry.newData ? sanitizeData(entry.newData) : null;\n        const sanitizedDetails = entry.details ? sanitizeData(entry.details) : null;\n        // Create audit log entry\n        const auditEntry = {\n            userId: entry.userId || null,\n            action: entry.action,\n            entity: entry.entity,\n            entityId: entry.entityId,\n            oldData: sanitizedOldData,\n            newData: sanitizedNewData,\n            ipAddress: entry.ipAddress,\n            userAgent: entry.userAgent,\n            level,\n            details: sanitizedDetails,\n            createdAt: timestamp\n        };\n        // Log to console for immediate visibility\n        const logLevel = level === \"critical\" ? \"error\" : level === \"warn\" ? \"warn\" : \"info\";\n        console[logLevel](`[AUDIT] ${entry.action}:`, {\n            userId: entry.userId,\n            entity: entry.entity,\n            entityId: entry.entityId,\n            ipAddress: entry.ipAddress,\n            timestamp: timestamp.toISOString()\n        });\n        // Store in database (create audit_logs table if it doesn't exist)\n        try {\n            // Note: In a real implementation, you would create an AuditLog model in Prisma\n            // For now, we'll store in a JSON file as fallback\n            await storeAuditLogFallback(auditEntry);\n        } catch (dbError) {\n            console.error(\"Failed to store audit log in database:\", dbError);\n            // Fallback to file logging\n            await storeAuditLogFallback(auditEntry);\n        }\n    } catch (error) {\n        console.error(\"Audit logging failed:\", error);\n    // Don't throw - audit logging should never break the main application\n    }\n}\n// Fallback audit log storage (file-based)\nasync function storeAuditLogFallback(entry) {\n    try {\n        const fs = (__webpack_require__(/*! fs */ \"fs\").promises);\n        const path = __webpack_require__(/*! path */ \"path\");\n        const logDir = path.join(process.cwd(), \"logs\");\n        const logFile = path.join(logDir, `audit-${new Date().toISOString().split(\"T\")[0]}.json`);\n        // Ensure logs directory exists\n        try {\n            await fs.mkdir(logDir, {\n                recursive: true\n            });\n        } catch (error) {\n        // Directory might already exist\n        }\n        // Read existing logs\n        let logs = [];\n        try {\n            const existingLogs = await fs.readFile(logFile, \"utf8\");\n            logs = JSON.parse(existingLogs);\n        } catch (error) {\n        // File doesn't exist or is empty\n        }\n        // Add new entry\n        logs.push(entry);\n        // Write back to file\n        await fs.writeFile(logFile, JSON.stringify(logs, null, 2));\n    } catch (error) {\n        console.error(\"Fallback audit logging failed:\", error);\n    }\n}\n// Audit log query functions\nasync function getAuditLogs(filters) {\n    try {\n        // In a real implementation, this would query the AuditLog model\n        // For now, return empty array\n        return [];\n    } catch (error) {\n        console.error(\"Failed to retrieve audit logs:\", error);\n        return [];\n    }\n}\n// Security monitoring functions\nasync function detectSuspiciousActivity() {\n    try {\n        // Implement suspicious activity detection logic\n        // Examples:\n        // - Multiple failed login attempts from same IP\n        // - Unusual access patterns\n        // - Access from new locations\n        // - Bulk data operations\n        console.log(\"Running suspicious activity detection...\");\n    } catch (error) {\n        console.error(\"Suspicious activity detection failed:\", error);\n    }\n}\n// Convenience functions for common audit events\nconst auditEvents = {\n    loginSuccess: (userId, ipAddress, userAgent)=>auditLog({\n            userId,\n            action: \"LOGIN_SUCCESS\",\n            entity: \"AUTH\",\n            entityId: userId,\n            ipAddress,\n            userAgent\n        }),\n    loginFailed: (email, ipAddress, userAgent)=>auditLog({\n            action: \"LOGIN_FAILED\",\n            entity: \"AUTH\",\n            entityId: email,\n            ipAddress,\n            userAgent,\n            details: {\n                email\n            }\n        }),\n    dataAccess: (userId, entity, entityId, ipAddress, userAgent)=>auditLog({\n            userId,\n            action: \"DATA_ACCESS\",\n            entity,\n            entityId,\n            ipAddress,\n            userAgent\n        }),\n    dataModification: (userId, entity, entityId, oldData, newData, ipAddress, userAgent)=>auditLog({\n            userId,\n            action: \"DATA_MODIFIED\",\n            entity,\n            entityId,\n            oldData,\n            newData,\n            ipAddress,\n            userAgent\n        }),\n    suspiciousActivity: (description, ipAddress, userAgent, details)=>auditLog({\n            action: \"SUSPICIOUS_ACTIVITY\",\n            entity: \"SECURITY\",\n            entityId: \"suspicious-activity\",\n            ipAddress,\n            userAgent,\n            details: {\n                description,\n                ...details\n            }\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1ZGl0LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztVQWVZQTs7Ozs7R0FBQUEsZUFBQUE7QUFPWixnREFBZ0Q7QUFDaEQsTUFBTUMsbUJBQW1CO0lBQ3ZCO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtDQUNEO0FBRUQsbURBQW1EO0FBQ25ELE1BQU1DLHFCQUFxQjtJQUN6QjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0Q7QUFFRCxtREFBbUQ7QUFDbkQsU0FBU0MsY0FBY0MsTUFBYyxFQUFFQyxNQUFjO0lBQ25ELElBQUlKLGlCQUFpQkssUUFBUSxDQUFDRixTQUFTO1FBQ3JDO0lBQ0Y7SUFFQSxJQUFJRixtQkFBbUJJLFFBQVEsQ0FBQ0QsT0FBT0UsV0FBVyxLQUFLO1FBQ3JEO0lBQ0Y7SUFFQSxJQUFJSCxPQUFPRSxRQUFRLENBQUMsYUFBYUYsT0FBT0UsUUFBUSxDQUFDLFdBQVc7UUFDMUQ7SUFDRjtJQUVBO0FBQ0Y7QUFFQSx5Q0FBeUM7QUFDekMsU0FBU0UsYUFBYUMsSUFBUztJQUM3QixJQUFJLENBQUNBLFFBQVEsT0FBT0EsU0FBUyxVQUFVO1FBQ3JDLE9BQU9BO0lBQ1Q7SUFFQSxNQUFNQyxrQkFBa0I7UUFBQztRQUFZO1FBQVM7UUFBVTtRQUFPO0tBQU87SUFDdEUsTUFBTUMsWUFBWTtRQUFFLEdBQUdGLElBQUk7SUFBQztJQUU1QixLQUFLLE1BQU1HLFNBQVNGLGdCQUFpQjtRQUNuQyxJQUFJRSxTQUFTRCxXQUFXO1lBQ3RCQSxTQUFTLENBQUNDLE1BQU0sR0FBRztRQUNyQjtJQUNGO0lBRUEsc0NBQXNDO0lBQ3RDLElBQUssTUFBTUMsT0FBT0YsVUFBVztRQUMzQixJQUFJLE9BQU9BLFNBQVMsQ0FBQ0UsSUFBSSxLQUFLLFlBQVlGLFNBQVMsQ0FBQ0UsSUFBSSxLQUFLLE1BQU07WUFDakVGLFNBQVMsQ0FBQ0UsSUFBSSxHQUFHTCxhQUFhRyxTQUFTLENBQUNFLElBQUk7UUFDOUM7SUFDRjtJQUVBLE9BQU9GO0FBQ1Q7QUFFQSw4QkFBOEI7QUFDdkIsZUFBZUcsU0FBU0MsS0FBb0I7SUFDakQsSUFBSTtRQUNGLE1BQU1DLGVBQWVDLFFBQVFDLEdBQUcsQ0FBQ0MsaUJBQWlCLEtBQUs7UUFFdkQsSUFBSSxDQUFDSCxjQUFjO1lBQ2pCO1FBQ0Y7UUFFQSxNQUFNSSxRQUFRakIsY0FBY1ksTUFBTVgsTUFBTSxFQUFFVyxNQUFNVixNQUFNO1FBQ3RELE1BQU1nQixZQUFZLElBQUlDO1FBRXRCLDBCQUEwQjtRQUMxQixNQUFNQyxtQkFBbUJSLE1BQU1TLE9BQU8sR0FBR2hCLGFBQWFPLE1BQU1TLE9BQU8sSUFBSTtRQUN2RSxNQUFNQyxtQkFBbUJWLE1BQU1XLE9BQU8sR0FBR2xCLGFBQWFPLE1BQU1XLE9BQU8sSUFBSTtRQUN2RSxNQUFNQyxtQkFBbUJaLE1BQU1hLE9BQU8sR0FBR3BCLGFBQWFPLE1BQU1hLE9BQU8sSUFBSTtRQUV2RSx5QkFBeUI7UUFDekIsTUFBTUMsYUFBYTtZQUNqQkMsUUFBUWYsTUFBTWUsTUFBTSxJQUFJO1lBQ3hCMUIsUUFBUVcsTUFBTVgsTUFBTTtZQUNwQkMsUUFBUVUsTUFBTVYsTUFBTTtZQUNwQjBCLFVBQVVoQixNQUFNZ0IsUUFBUTtZQUN4QlAsU0FBU0Q7WUFDVEcsU0FBU0Q7WUFDVE8sV0FBV2pCLE1BQU1pQixTQUFTO1lBQzFCQyxXQUFXbEIsTUFBTWtCLFNBQVM7WUFDMUJiO1lBQ0FRLFNBQVNEO1lBQ1RPLFdBQVdiO1FBQ2I7UUFFQSwwQ0FBMEM7UUFDMUMsTUFBTWMsV0FBV2YsdUJBQWdDLFVBQ2pDQSxtQkFBNEIsU0FBUztRQUVyRGdCLE9BQU8sQ0FBQ0QsU0FBUyxDQUFDLENBQUMsUUFBUSxFQUFFcEIsTUFBTVgsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFO1lBQzVDMEIsUUFBUWYsTUFBTWUsTUFBTTtZQUNwQnpCLFFBQVFVLE1BQU1WLE1BQU07WUFDcEIwQixVQUFVaEIsTUFBTWdCLFFBQVE7WUFDeEJDLFdBQVdqQixNQUFNaUIsU0FBUztZQUMxQlgsV0FBV0EsVUFBVWdCLFdBQVc7UUFDbEM7UUFFQSxrRUFBa0U7UUFDbEUsSUFBSTtZQUNGLCtFQUErRTtZQUMvRSxrREFBa0Q7WUFDbEQsTUFBTUMsc0JBQXNCVDtRQUM5QixFQUFFLE9BQU9VLFNBQVM7WUFDaEJILFFBQVFJLEtBQUssQ0FBQywwQ0FBMENEO1lBQ3hELDJCQUEyQjtZQUMzQixNQUFNRCxzQkFBc0JUO1FBQzlCO0lBRUYsRUFBRSxPQUFPVyxPQUFPO1FBQ2RKLFFBQVFJLEtBQUssQ0FBQyx5QkFBeUJBO0lBQ3ZDLHNFQUFzRTtJQUN4RTtBQUNGO0FBRUEsMENBQTBDO0FBQzFDLGVBQWVGLHNCQUFzQnZCLEtBQVU7SUFDN0MsSUFBSTtRQUNGLE1BQU0wQixLQUFLQyw4Q0FBc0I7UUFDakMsTUFBTUUsT0FBT0YsbUJBQU9BLENBQUM7UUFFckIsTUFBTUcsU0FBU0QsS0FBS0UsSUFBSSxDQUFDN0IsUUFBUThCLEdBQUcsSUFBSTtRQUN4QyxNQUFNQyxVQUFVSixLQUFLRSxJQUFJLENBQUNELFFBQVEsQ0FBQyxNQUFNLEVBQUUsSUFBSXZCLE9BQU9lLFdBQVcsR0FBR1ksS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsS0FBSyxDQUFDO1FBRXhGLCtCQUErQjtRQUMvQixJQUFJO1lBQ0YsTUFBTVIsR0FBR1MsS0FBSyxDQUFDTCxRQUFRO2dCQUFFTSxXQUFXO1lBQUs7UUFDM0MsRUFBRSxPQUFPWCxPQUFPO1FBQ2QsZ0NBQWdDO1FBQ2xDO1FBRUEscUJBQXFCO1FBQ3JCLElBQUlZLE9BQU8sRUFBRTtRQUNiLElBQUk7WUFDRixNQUFNQyxlQUFlLE1BQU1aLEdBQUdhLFFBQVEsQ0FBQ04sU0FBUztZQUNoREksT0FBT0csS0FBS0MsS0FBSyxDQUFDSDtRQUNwQixFQUFFLE9BQU9iLE9BQU87UUFDZCxpQ0FBaUM7UUFDbkM7UUFFQSxnQkFBZ0I7UUFDaEJZLEtBQUtLLElBQUksQ0FBQzFDO1FBRVYscUJBQXFCO1FBQ3JCLE1BQU0wQixHQUFHaUIsU0FBUyxDQUFDVixTQUFTTyxLQUFLSSxTQUFTLENBQUNQLE1BQU0sTUFBTTtJQUV6RCxFQUFFLE9BQU9aLE9BQU87UUFDZEosUUFBUUksS0FBSyxDQUFDLGtDQUFrQ0E7SUFDbEQ7QUFDRjtBQUVBLDRCQUE0QjtBQUNyQixlQUFlb0IsYUFBYUMsT0FRbEM7SUFDQyxJQUFJO1FBQ0YsZ0VBQWdFO1FBQ2hFLDhCQUE4QjtRQUM5QixPQUFPLEVBQUU7SUFDWCxFQUFFLE9BQU9yQixPQUFPO1FBQ2RKLFFBQVFJLEtBQUssQ0FBQyxrQ0FBa0NBO1FBQ2hELE9BQU8sRUFBRTtJQUNYO0FBQ0Y7QUFFQSxnQ0FBZ0M7QUFDekIsZUFBZXNCO0lBQ3BCLElBQUk7UUFDRixnREFBZ0Q7UUFDaEQsWUFBWTtRQUNaLGdEQUFnRDtRQUNoRCw0QkFBNEI7UUFDNUIsOEJBQThCO1FBQzlCLHlCQUF5QjtRQUV6QjFCLFFBQVEyQixHQUFHLENBQUM7SUFFZCxFQUFFLE9BQU92QixPQUFPO1FBQ2RKLFFBQVFJLEtBQUssQ0FBQyx5Q0FBeUNBO0lBQ3pEO0FBQ0Y7QUFFQSxnREFBZ0Q7QUFDekMsTUFBTXdCLGNBQWM7SUFDekJDLGNBQWMsQ0FBQ25DLFFBQWdCRSxXQUFtQkMsWUFDaERuQixTQUFTO1lBQ1BnQjtZQUNBMUIsUUFBUTtZQUNSQyxRQUFRO1lBQ1IwQixVQUFVRDtZQUNWRTtZQUNBQztRQUNGO0lBRUZpQyxhQUFhLENBQUNDLE9BQWVuQyxXQUFtQkMsWUFDOUNuQixTQUFTO1lBQ1BWLFFBQVE7WUFDUkMsUUFBUTtZQUNSMEIsVUFBVW9DO1lBQ1ZuQztZQUNBQztZQUNBTCxTQUFTO2dCQUFFdUM7WUFBTTtRQUNuQjtJQUVGQyxZQUFZLENBQUN0QyxRQUFnQnpCLFFBQWdCMEIsVUFBa0JDLFdBQW1CQyxZQUNoRm5CLFNBQVM7WUFDUGdCO1lBQ0ExQixRQUFRO1lBQ1JDO1lBQ0EwQjtZQUNBQztZQUNBQztRQUNGO0lBRUZvQyxrQkFBa0IsQ0FBQ3ZDLFFBQWdCekIsUUFBZ0IwQixVQUFrQlAsU0FBY0UsU0FBY00sV0FBbUJDLFlBQ2xIbkIsU0FBUztZQUNQZ0I7WUFDQTFCLFFBQVE7WUFDUkM7WUFDQTBCO1lBQ0FQO1lBQ0FFO1lBQ0FNO1lBQ0FDO1FBQ0Y7SUFFRnFDLG9CQUFvQixDQUFDQyxhQUFxQnZDLFdBQW1CQyxXQUFtQkwsVUFDOUVkLFNBQVM7WUFDUFYsUUFBUTtZQUNSQyxRQUFRO1lBQ1IwQixVQUFVO1lBQ1ZDO1lBQ0FDO1lBQ0FMLFNBQVM7Z0JBQUUyQztnQkFBYSxHQUFHM0MsT0FBTztZQUFDO1FBQ3JDO0FBQ0osRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL21vZGVybml6ZS1uZXh0LWZyZWUvLi9zcmMvbGliL2F1ZGl0LnRzP2Y0NjUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcHJpc21hIH0gZnJvbSAnQC9saWIvcHJpc21hJztcblxuZXhwb3J0IGludGVyZmFjZSBBdWRpdExvZ0VudHJ5IHtcbiAgdXNlcklkPzogc3RyaW5nO1xuICBhY3Rpb246IHN0cmluZztcbiAgZW50aXR5OiBzdHJpbmc7XG4gIGVudGl0eUlkOiBzdHJpbmc7XG4gIG9sZERhdGE/OiBhbnk7XG4gIG5ld0RhdGE/OiBhbnk7XG4gIGlwQWRkcmVzczogc3RyaW5nO1xuICB1c2VyQWdlbnQ6IHN0cmluZztcbiAgZGV0YWlscz86IGFueTtcbn1cblxuLy8gQXVkaXQgbG9nIGxldmVsc1xuZXhwb3J0IGVudW0gQXVkaXRMZXZlbCB7XG4gIElORk8gPSAnaW5mbycsXG4gIFdBUk4gPSAnd2FybicsXG4gIEVSUk9SID0gJ2Vycm9yJyxcbiAgQ1JJVElDQUwgPSAnY3JpdGljYWwnXG59XG5cbi8vIENyaXRpY2FsIGFjdGlvbnMgdGhhdCBzaG91bGQgYWx3YXlzIGJlIGxvZ2dlZFxuY29uc3QgQ1JJVElDQUxfQUNUSU9OUyA9IFtcbiAgJ0xPR0lOX1NVQ0NFU1MnLFxuICAnTE9HSU5fRkFJTEVEJyxcbiAgJ0xPR09VVCcsXG4gICdQQVNTV09SRF9DSEFOR0UnLFxuICAnVVNFUl9DUkVBVEVEJyxcbiAgJ1VTRVJfREVMRVRFRCcsXG4gICdQRVJNSVNTSU9OX0NIQU5HRScsXG4gICdEQVRBX0VYUE9SVCcsXG4gICdTWVNURU1fQ09ORklHX0NIQU5HRScsXG4gICdVTkFVVEhPUklaRURfQUNDRVNTX0FUVEVNUFQnLFxuICAnUkFURV9MSU1JVF9FWENFRURFRCcsXG4gICdTVVNQSUNJT1VTX0FDVElWSVRZJ1xuXTtcblxuLy8gSGlnaC1yaXNrIGVudGl0aWVzIHRoYXQgcmVxdWlyZSBkZXRhaWxlZCBsb2dnaW5nXG5jb25zdCBISUdIX1JJU0tfRU5USVRJRVMgPSBbXG4gICdVU0VSJyxcbiAgJ0FETUlOJyxcbiAgJ1RFQUNIRVInLFxuICAnUEFZTUVOVCcsXG4gICdTWVNURU1fQ09ORklHJ1xuXTtcblxuLy8gRGV0ZXJtaW5lIGF1ZGl0IGxldmVsIGJhc2VkIG9uIGFjdGlvbiBhbmQgZW50aXR5XG5mdW5jdGlvbiBnZXRBdWRpdExldmVsKGFjdGlvbjogc3RyaW5nLCBlbnRpdHk6IHN0cmluZyk6IEF1ZGl0TGV2ZWwge1xuICBpZiAoQ1JJVElDQUxfQUNUSU9OUy5pbmNsdWRlcyhhY3Rpb24pKSB7XG4gICAgcmV0dXJuIEF1ZGl0TGV2ZWwuQ1JJVElDQUw7XG4gIH1cbiAgXG4gIGlmIChISUdIX1JJU0tfRU5USVRJRVMuaW5jbHVkZXMoZW50aXR5LnRvVXBwZXJDYXNlKCkpKSB7XG4gICAgcmV0dXJuIEF1ZGl0TGV2ZWwuV0FSTjtcbiAgfVxuICBcbiAgaWYgKGFjdGlvbi5pbmNsdWRlcygnREVMRVRFJykgfHwgYWN0aW9uLmluY2x1ZGVzKCdSRU1PVkUnKSkge1xuICAgIHJldHVybiBBdWRpdExldmVsLldBUk47XG4gIH1cbiAgXG4gIHJldHVybiBBdWRpdExldmVsLklORk87XG59XG5cbi8vIFNhbml0aXplIHNlbnNpdGl2ZSBkYXRhIGJlZm9yZSBsb2dnaW5nXG5mdW5jdGlvbiBzYW5pdGl6ZURhdGEoZGF0YTogYW55KTogYW55IHtcbiAgaWYgKCFkYXRhIHx8IHR5cGVvZiBkYXRhICE9PSAnb2JqZWN0Jykge1xuICAgIHJldHVybiBkYXRhO1xuICB9XG4gIFxuICBjb25zdCBzZW5zaXRpdmVGaWVsZHMgPSBbJ3Bhc3N3b3JkJywgJ3Rva2VuJywgJ3NlY3JldCcsICdrZXknLCAnaGFzaCddO1xuICBjb25zdCBzYW5pdGl6ZWQgPSB7IC4uLmRhdGEgfTtcbiAgXG4gIGZvciAoY29uc3QgZmllbGQgb2Ygc2Vuc2l0aXZlRmllbGRzKSB7XG4gICAgaWYgKGZpZWxkIGluIHNhbml0aXplZCkge1xuICAgICAgc2FuaXRpemVkW2ZpZWxkXSA9ICdbUkVEQUNURURdJztcbiAgICB9XG4gIH1cbiAgXG4gIC8vIFJlY3Vyc2l2ZWx5IHNhbml0aXplIG5lc3RlZCBvYmplY3RzXG4gIGZvciAoY29uc3Qga2V5IGluIHNhbml0aXplZCkge1xuICAgIGlmICh0eXBlb2Ygc2FuaXRpemVkW2tleV0gPT09ICdvYmplY3QnICYmIHNhbml0aXplZFtrZXldICE9PSBudWxsKSB7XG4gICAgICBzYW5pdGl6ZWRba2V5XSA9IHNhbml0aXplRGF0YShzYW5pdGl6ZWRba2V5XSk7XG4gICAgfVxuICB9XG4gIFxuICByZXR1cm4gc2FuaXRpemVkO1xufVxuXG4vLyBNYWluIGF1ZGl0IGxvZ2dpbmcgZnVuY3Rpb25cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBhdWRpdExvZyhlbnRyeTogQXVkaXRMb2dFbnRyeSk6IFByb21pc2U8dm9pZD4ge1xuICB0cnkge1xuICAgIGNvbnN0IGF1ZGl0RW5hYmxlZCA9IHByb2Nlc3MuZW52LkFVRElUX0xPR19FTkFCTEVEID09PSAndHJ1ZSc7XG4gICAgXG4gICAgaWYgKCFhdWRpdEVuYWJsZWQpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgXG4gICAgY29uc3QgbGV2ZWwgPSBnZXRBdWRpdExldmVsKGVudHJ5LmFjdGlvbiwgZW50cnkuZW50aXR5KTtcbiAgICBjb25zdCB0aW1lc3RhbXAgPSBuZXcgRGF0ZSgpO1xuICAgIFxuICAgIC8vIFNhbml0aXplIHNlbnNpdGl2ZSBkYXRhXG4gICAgY29uc3Qgc2FuaXRpemVkT2xkRGF0YSA9IGVudHJ5Lm9sZERhdGEgPyBzYW5pdGl6ZURhdGEoZW50cnkub2xkRGF0YSkgOiBudWxsO1xuICAgIGNvbnN0IHNhbml0aXplZE5ld0RhdGEgPSBlbnRyeS5uZXdEYXRhID8gc2FuaXRpemVEYXRhKGVudHJ5Lm5ld0RhdGEpIDogbnVsbDtcbiAgICBjb25zdCBzYW5pdGl6ZWREZXRhaWxzID0gZW50cnkuZGV0YWlscyA/IHNhbml0aXplRGF0YShlbnRyeS5kZXRhaWxzKSA6IG51bGw7XG4gICAgXG4gICAgLy8gQ3JlYXRlIGF1ZGl0IGxvZyBlbnRyeVxuICAgIGNvbnN0IGF1ZGl0RW50cnkgPSB7XG4gICAgICB1c2VySWQ6IGVudHJ5LnVzZXJJZCB8fCBudWxsLFxuICAgICAgYWN0aW9uOiBlbnRyeS5hY3Rpb24sXG4gICAgICBlbnRpdHk6IGVudHJ5LmVudGl0eSxcbiAgICAgIGVudGl0eUlkOiBlbnRyeS5lbnRpdHlJZCxcbiAgICAgIG9sZERhdGE6IHNhbml0aXplZE9sZERhdGEsXG4gICAgICBuZXdEYXRhOiBzYW5pdGl6ZWROZXdEYXRhLFxuICAgICAgaXBBZGRyZXNzOiBlbnRyeS5pcEFkZHJlc3MsXG4gICAgICB1c2VyQWdlbnQ6IGVudHJ5LnVzZXJBZ2VudCxcbiAgICAgIGxldmVsLFxuICAgICAgZGV0YWlsczogc2FuaXRpemVkRGV0YWlscyxcbiAgICAgIGNyZWF0ZWRBdDogdGltZXN0YW1wXG4gICAgfTtcbiAgICBcbiAgICAvLyBMb2cgdG8gY29uc29sZSBmb3IgaW1tZWRpYXRlIHZpc2liaWxpdHlcbiAgICBjb25zdCBsb2dMZXZlbCA9IGxldmVsID09PSBBdWRpdExldmVsLkNSSVRJQ0FMID8gJ2Vycm9yJyA6IFxuICAgICAgICAgICAgICAgICAgICBsZXZlbCA9PT0gQXVkaXRMZXZlbC5XQVJOID8gJ3dhcm4nIDogJ2luZm8nO1xuICAgIFxuICAgIGNvbnNvbGVbbG9nTGV2ZWxdKGBbQVVESVRdICR7ZW50cnkuYWN0aW9ufTpgLCB7XG4gICAgICB1c2VySWQ6IGVudHJ5LnVzZXJJZCxcbiAgICAgIGVudGl0eTogZW50cnkuZW50aXR5LFxuICAgICAgZW50aXR5SWQ6IGVudHJ5LmVudGl0eUlkLFxuICAgICAgaXBBZGRyZXNzOiBlbnRyeS5pcEFkZHJlc3MsXG4gICAgICB0aW1lc3RhbXA6IHRpbWVzdGFtcC50b0lTT1N0cmluZygpXG4gICAgfSk7XG4gICAgXG4gICAgLy8gU3RvcmUgaW4gZGF0YWJhc2UgKGNyZWF0ZSBhdWRpdF9sb2dzIHRhYmxlIGlmIGl0IGRvZXNuJ3QgZXhpc3QpXG4gICAgdHJ5IHtcbiAgICAgIC8vIE5vdGU6IEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgeW91IHdvdWxkIGNyZWF0ZSBhbiBBdWRpdExvZyBtb2RlbCBpbiBQcmlzbWFcbiAgICAgIC8vIEZvciBub3csIHdlJ2xsIHN0b3JlIGluIGEgSlNPTiBmaWxlIGFzIGZhbGxiYWNrXG4gICAgICBhd2FpdCBzdG9yZUF1ZGl0TG9nRmFsbGJhY2soYXVkaXRFbnRyeSk7XG4gICAgfSBjYXRjaCAoZGJFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHN0b3JlIGF1ZGl0IGxvZyBpbiBkYXRhYmFzZTonLCBkYkVycm9yKTtcbiAgICAgIC8vIEZhbGxiYWNrIHRvIGZpbGUgbG9nZ2luZ1xuICAgICAgYXdhaXQgc3RvcmVBdWRpdExvZ0ZhbGxiYWNrKGF1ZGl0RW50cnkpO1xuICAgIH1cbiAgICBcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdBdWRpdCBsb2dnaW5nIGZhaWxlZDonLCBlcnJvcik7XG4gICAgLy8gRG9uJ3QgdGhyb3cgLSBhdWRpdCBsb2dnaW5nIHNob3VsZCBuZXZlciBicmVhayB0aGUgbWFpbiBhcHBsaWNhdGlvblxuICB9XG59XG5cbi8vIEZhbGxiYWNrIGF1ZGl0IGxvZyBzdG9yYWdlIChmaWxlLWJhc2VkKVxuYXN5bmMgZnVuY3Rpb24gc3RvcmVBdWRpdExvZ0ZhbGxiYWNrKGVudHJ5OiBhbnkpOiBQcm9taXNlPHZvaWQ+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBmcyA9IHJlcXVpcmUoJ2ZzJykucHJvbWlzZXM7XG4gICAgY29uc3QgcGF0aCA9IHJlcXVpcmUoJ3BhdGgnKTtcbiAgICBcbiAgICBjb25zdCBsb2dEaXIgPSBwYXRoLmpvaW4ocHJvY2Vzcy5jd2QoKSwgJ2xvZ3MnKTtcbiAgICBjb25zdCBsb2dGaWxlID0gcGF0aC5qb2luKGxvZ0RpciwgYGF1ZGl0LSR7bmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF19Lmpzb25gKTtcbiAgICBcbiAgICAvLyBFbnN1cmUgbG9ncyBkaXJlY3RvcnkgZXhpc3RzXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IGZzLm1rZGlyKGxvZ0RpciwgeyByZWN1cnNpdmU6IHRydWUgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIERpcmVjdG9yeSBtaWdodCBhbHJlYWR5IGV4aXN0XG4gICAgfVxuICAgIFxuICAgIC8vIFJlYWQgZXhpc3RpbmcgbG9nc1xuICAgIGxldCBsb2dzID0gW107XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGV4aXN0aW5nTG9ncyA9IGF3YWl0IGZzLnJlYWRGaWxlKGxvZ0ZpbGUsICd1dGY4Jyk7XG4gICAgICBsb2dzID0gSlNPTi5wYXJzZShleGlzdGluZ0xvZ3MpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAvLyBGaWxlIGRvZXNuJ3QgZXhpc3Qgb3IgaXMgZW1wdHlcbiAgICB9XG4gICAgXG4gICAgLy8gQWRkIG5ldyBlbnRyeVxuICAgIGxvZ3MucHVzaChlbnRyeSk7XG4gICAgXG4gICAgLy8gV3JpdGUgYmFjayB0byBmaWxlXG4gICAgYXdhaXQgZnMud3JpdGVGaWxlKGxvZ0ZpbGUsIEpTT04uc3RyaW5naWZ5KGxvZ3MsIG51bGwsIDIpKTtcbiAgICBcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdGYWxsYmFjayBhdWRpdCBsb2dnaW5nIGZhaWxlZDonLCBlcnJvcik7XG4gIH1cbn1cblxuLy8gQXVkaXQgbG9nIHF1ZXJ5IGZ1bmN0aW9uc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldEF1ZGl0TG9ncyhmaWx0ZXJzOiB7XG4gIHVzZXJJZD86IHN0cmluZztcbiAgYWN0aW9uPzogc3RyaW5nO1xuICBlbnRpdHk/OiBzdHJpbmc7XG4gIHN0YXJ0RGF0ZT86IERhdGU7XG4gIGVuZERhdGU/OiBEYXRlO1xuICBsaW1pdD86IG51bWJlcjtcbiAgb2Zmc2V0PzogbnVtYmVyO1xufSk6IFByb21pc2U8YW55W10+IHtcbiAgdHJ5IHtcbiAgICAvLyBJbiBhIHJlYWwgaW1wbGVtZW50YXRpb24sIHRoaXMgd291bGQgcXVlcnkgdGhlIEF1ZGl0TG9nIG1vZGVsXG4gICAgLy8gRm9yIG5vdywgcmV0dXJuIGVtcHR5IGFycmF5XG4gICAgcmV0dXJuIFtdO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byByZXRyaWV2ZSBhdWRpdCBsb2dzOicsIGVycm9yKTtcbiAgICByZXR1cm4gW107XG4gIH1cbn1cblxuLy8gU2VjdXJpdHkgbW9uaXRvcmluZyBmdW5jdGlvbnNcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBkZXRlY3RTdXNwaWNpb3VzQWN0aXZpdHkoKTogUHJvbWlzZTx2b2lkPiB7XG4gIHRyeSB7XG4gICAgLy8gSW1wbGVtZW50IHN1c3BpY2lvdXMgYWN0aXZpdHkgZGV0ZWN0aW9uIGxvZ2ljXG4gICAgLy8gRXhhbXBsZXM6XG4gICAgLy8gLSBNdWx0aXBsZSBmYWlsZWQgbG9naW4gYXR0ZW1wdHMgZnJvbSBzYW1lIElQXG4gICAgLy8gLSBVbnVzdWFsIGFjY2VzcyBwYXR0ZXJuc1xuICAgIC8vIC0gQWNjZXNzIGZyb20gbmV3IGxvY2F0aW9uc1xuICAgIC8vIC0gQnVsayBkYXRhIG9wZXJhdGlvbnNcbiAgICBcbiAgICBjb25zb2xlLmxvZygnUnVubmluZyBzdXNwaWNpb3VzIGFjdGl2aXR5IGRldGVjdGlvbi4uLicpO1xuICAgIFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1N1c3BpY2lvdXMgYWN0aXZpdHkgZGV0ZWN0aW9uIGZhaWxlZDonLCBlcnJvcik7XG4gIH1cbn1cblxuLy8gQ29udmVuaWVuY2UgZnVuY3Rpb25zIGZvciBjb21tb24gYXVkaXQgZXZlbnRzXG5leHBvcnQgY29uc3QgYXVkaXRFdmVudHMgPSB7XG4gIGxvZ2luU3VjY2VzczogKHVzZXJJZDogc3RyaW5nLCBpcEFkZHJlc3M6IHN0cmluZywgdXNlckFnZW50OiBzdHJpbmcpID0+XG4gICAgYXVkaXRMb2coe1xuICAgICAgdXNlcklkLFxuICAgICAgYWN0aW9uOiAnTE9HSU5fU1VDQ0VTUycsXG4gICAgICBlbnRpdHk6ICdBVVRIJyxcbiAgICAgIGVudGl0eUlkOiB1c2VySWQsXG4gICAgICBpcEFkZHJlc3MsXG4gICAgICB1c2VyQWdlbnRcbiAgICB9KSxcbiAgICBcbiAgbG9naW5GYWlsZWQ6IChlbWFpbDogc3RyaW5nLCBpcEFkZHJlc3M6IHN0cmluZywgdXNlckFnZW50OiBzdHJpbmcpID0+XG4gICAgYXVkaXRMb2coe1xuICAgICAgYWN0aW9uOiAnTE9HSU5fRkFJTEVEJyxcbiAgICAgIGVudGl0eTogJ0FVVEgnLFxuICAgICAgZW50aXR5SWQ6IGVtYWlsLFxuICAgICAgaXBBZGRyZXNzLFxuICAgICAgdXNlckFnZW50LFxuICAgICAgZGV0YWlsczogeyBlbWFpbCB9XG4gICAgfSksXG4gICAgXG4gIGRhdGFBY2Nlc3M6ICh1c2VySWQ6IHN0cmluZywgZW50aXR5OiBzdHJpbmcsIGVudGl0eUlkOiBzdHJpbmcsIGlwQWRkcmVzczogc3RyaW5nLCB1c2VyQWdlbnQ6IHN0cmluZykgPT5cbiAgICBhdWRpdExvZyh7XG4gICAgICB1c2VySWQsXG4gICAgICBhY3Rpb246ICdEQVRBX0FDQ0VTUycsXG4gICAgICBlbnRpdHksXG4gICAgICBlbnRpdHlJZCxcbiAgICAgIGlwQWRkcmVzcyxcbiAgICAgIHVzZXJBZ2VudFxuICAgIH0pLFxuICAgIFxuICBkYXRhTW9kaWZpY2F0aW9uOiAodXNlcklkOiBzdHJpbmcsIGVudGl0eTogc3RyaW5nLCBlbnRpdHlJZDogc3RyaW5nLCBvbGREYXRhOiBhbnksIG5ld0RhdGE6IGFueSwgaXBBZGRyZXNzOiBzdHJpbmcsIHVzZXJBZ2VudDogc3RyaW5nKSA9PlxuICAgIGF1ZGl0TG9nKHtcbiAgICAgIHVzZXJJZCxcbiAgICAgIGFjdGlvbjogJ0RBVEFfTU9ESUZJRUQnLFxuICAgICAgZW50aXR5LFxuICAgICAgZW50aXR5SWQsXG4gICAgICBvbGREYXRhLFxuICAgICAgbmV3RGF0YSxcbiAgICAgIGlwQWRkcmVzcyxcbiAgICAgIHVzZXJBZ2VudFxuICAgIH0pLFxuICAgIFxuICBzdXNwaWNpb3VzQWN0aXZpdHk6IChkZXNjcmlwdGlvbjogc3RyaW5nLCBpcEFkZHJlc3M6IHN0cmluZywgdXNlckFnZW50OiBzdHJpbmcsIGRldGFpbHM/OiBhbnkpID0+XG4gICAgYXVkaXRMb2coe1xuICAgICAgYWN0aW9uOiAnU1VTUElDSU9VU19BQ1RJVklUWScsXG4gICAgICBlbnRpdHk6ICdTRUNVUklUWScsXG4gICAgICBlbnRpdHlJZDogJ3N1c3BpY2lvdXMtYWN0aXZpdHknLFxuICAgICAgaXBBZGRyZXNzLFxuICAgICAgdXNlckFnZW50LFxuICAgICAgZGV0YWlsczogeyBkZXNjcmlwdGlvbiwgLi4uZGV0YWlscyB9XG4gICAgfSlcbn07XG4iXSwibmFtZXMiOlsiQXVkaXRMZXZlbCIsIkNSSVRJQ0FMX0FDVElPTlMiLCJISUdIX1JJU0tfRU5USVRJRVMiLCJnZXRBdWRpdExldmVsIiwiYWN0aW9uIiwiZW50aXR5IiwiaW5jbHVkZXMiLCJ0b1VwcGVyQ2FzZSIsInNhbml0aXplRGF0YSIsImRhdGEiLCJzZW5zaXRpdmVGaWVsZHMiLCJzYW5pdGl6ZWQiLCJmaWVsZCIsImtleSIsImF1ZGl0TG9nIiwiZW50cnkiLCJhdWRpdEVuYWJsZWQiLCJwcm9jZXNzIiwiZW52IiwiQVVESVRfTE9HX0VOQUJMRUQiLCJsZXZlbCIsInRpbWVzdGFtcCIsIkRhdGUiLCJzYW5pdGl6ZWRPbGREYXRhIiwib2xkRGF0YSIsInNhbml0aXplZE5ld0RhdGEiLCJuZXdEYXRhIiwic2FuaXRpemVkRGV0YWlscyIsImRldGFpbHMiLCJhdWRpdEVudHJ5IiwidXNlcklkIiwiZW50aXR5SWQiLCJpcEFkZHJlc3MiLCJ1c2VyQWdlbnQiLCJjcmVhdGVkQXQiLCJsb2dMZXZlbCIsImNvbnNvbGUiLCJ0b0lTT1N0cmluZyIsInN0b3JlQXVkaXRMb2dGYWxsYmFjayIsImRiRXJyb3IiLCJlcnJvciIsImZzIiwicmVxdWlyZSIsInByb21pc2VzIiwicGF0aCIsImxvZ0RpciIsImpvaW4iLCJjd2QiLCJsb2dGaWxlIiwic3BsaXQiLCJta2RpciIsInJlY3Vyc2l2ZSIsImxvZ3MiLCJleGlzdGluZ0xvZ3MiLCJyZWFkRmlsZSIsIkpTT04iLCJwYXJzZSIsInB1c2giLCJ3cml0ZUZpbGUiLCJzdHJpbmdpZnkiLCJnZXRBdWRpdExvZ3MiLCJmaWx0ZXJzIiwiZGV0ZWN0U3VzcGljaW91c0FjdGl2aXR5IiwibG9nIiwiYXVkaXRFdmVudHMiLCJsb2dpblN1Y2Nlc3MiLCJsb2dpbkZhaWxlZCIsImVtYWlsIiwiZGF0YUFjY2VzcyIsImRhdGFNb2RpZmljYXRpb24iLCJzdXNwaWNpb3VzQWN0aXZpdHkiLCJkZXNjcmlwdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/audit.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticate: () => (/* binding */ authenticate),\n/* harmony export */   extractTokenFromRequest: () => (/* binding */ extractTokenFromRequest),\n/* harmony export */   generateRefreshToken: () => (/* binding */ generateRefreshToken),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth),\n/* harmony export */   requireRole: () => (/* binding */ requireRole),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Secure admin configuration from environment\nconst ADMIN_USERS = [\n    {\n        id: \"admin-parviz\",\n        email: process.env.ADMIN_PARVIZ_EMAIL || \"<EMAIL>\",\n        passwordHash: process.env.ADMIN_PARVIZ_PASSWORD_HASH || \"\",\n        name: \"Parviz\",\n        role: \"ADMIN\",\n        permissions: [\n            \"*\"\n        ] // Full access\n    },\n    {\n        id: \"admin-reception\",\n        email: process.env.ADMIN_RECEPTION_EMAIL || \"<EMAIL>\",\n        passwordHash: process.env.ADMIN_RECEPTION_PASSWORD_HASH || \"\",\n        name: \"Reception\",\n        role: \"RECEPTION\",\n        permissions: [\n            \"students:read\",\n            \"students:write\",\n            \"classes:read\",\n            \"payments:read\"\n        ]\n    },\n    {\n        id: \"admin-mukhammadkhon\",\n        email: process.env.ADMIN_MUKHAMMADKHON_EMAIL || \"<EMAIL>\",\n        passwordHash: process.env.ADMIN_MUKHAMMADKHON_PASSWORD_HASH || \"\",\n        name: \"Mukhammadkhon\",\n        role: \"ADMIN\",\n        permissions: [\n            \"*\"\n        ] // Full access\n    }\n];\nconst JWT_SECRET = process.env.JWT_SECRET || \"fallback-secret-change-immediately\";\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || \"24h\";\nconst JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || \"7d\";\n// Hash password utility\nasync function hashPassword(password) {\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, saltRounds);\n}\n// Verify password utility\nasync function verifyPassword(password, hash) {\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hash);\n}\n// Generate JWT token\nfunction generateToken(user) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign({\n        userId: user.id,\n        email: user.email,\n        role: user.role,\n        permissions: user.permissions\n    }, JWT_SECRET, {\n        expiresIn: JWT_EXPIRES_IN\n    });\n}\n// Generate refresh token\nfunction generateRefreshToken(user) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign({\n        userId: user.id\n    }, JWT_SECRET, {\n        expiresIn: JWT_REFRESH_EXPIRES_IN\n    });\n}\n// Verify JWT token\nfunction verifyToken(token) {\n    try {\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        return {\n            id: decoded.userId,\n            email: decoded.email,\n            name: decoded.name || \"\",\n            role: decoded.role,\n            permissions: decoded.permissions || []\n        };\n    } catch (error) {\n        return null;\n    }\n}\n// Authenticate user with email and password\nasync function authenticate(email, password) {\n    try {\n        // Find user by email\n        const adminUser = ADMIN_USERS.find((user)=>user.email.toLowerCase() === email.toLowerCase());\n        if (!adminUser) {\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Verify password\n        const isValidPassword = await verifyPassword(password, adminUser.passwordHash);\n        if (!isValidPassword) {\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Create user object\n        const user = {\n            id: adminUser.id,\n            email: adminUser.email,\n            name: adminUser.name,\n            role: adminUser.role,\n            permissions: adminUser.permissions\n        };\n        // Generate tokens\n        const token = generateToken(user);\n        const refreshToken = generateRefreshToken(user);\n        return {\n            success: true,\n            user,\n            token,\n            refreshToken\n        };\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        return {\n            success: false,\n            error: \"Authentication failed\"\n        };\n    }\n}\n// Extract token from request\nfunction extractTokenFromRequest(request) {\n    const authHeader = request.headers.get(\"authorization\");\n    if (authHeader && authHeader.startsWith(\"Bearer \")) {\n        return authHeader.substring(7);\n    }\n    // Also check cookies as fallback\n    const tokenCookie = request.cookies.get(\"auth-token\");\n    return tokenCookie?.value || null;\n}\n// Middleware to verify authentication\nfunction requireAuth(request) {\n    const token = extractTokenFromRequest(request);\n    if (!token) {\n        return null;\n    }\n    return verifyToken(token);\n}\n// Check if user has specific permission\nfunction hasPermission(user, permission) {\n    if (user.permissions.includes(\"*\")) {\n        return true; // Admin has all permissions\n    }\n    return user.permissions.includes(permission);\n}\n// Role-based access control\nfunction requireRole(user, allowedRoles) {\n    return allowedRoles.includes(user.role);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/middleware/auth.ts":
/*!********************************!*\
  !*** ./src/middleware/auth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rateLimit: () => (/* binding */ rateLimit),\n/* harmony export */   validateInput: () => (/* binding */ validateInput),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_audit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/audit */ \"(rsc)/./src/lib/audit.ts\");\n\n\n\n// Rate limiting store (in production, use Redis)\nconst rateLimitStore = new Map();\n// Rate limiting configuration\nconst RATE_LIMIT_WINDOW_MS = parseInt(process.env.RATE_LIMIT_WINDOW_MS || \"900000\"); // 15 minutes\nconst RATE_LIMIT_MAX_REQUESTS = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || \"100\");\n// Rate limiting middleware\nfunction rateLimit(request) {\n    const clientIP = request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\";\n    const now = Date.now();\n    // Clean up expired entries\n    for (const [ip, data] of rateLimitStore.entries()){\n        if (now > data.resetTime) {\n            rateLimitStore.delete(ip);\n        }\n    }\n    // Get or create rate limit data for this IP\n    let rateLimitData = rateLimitStore.get(clientIP);\n    if (!rateLimitData || now > rateLimitData.resetTime) {\n        rateLimitData = {\n            count: 1,\n            resetTime: now + RATE_LIMIT_WINDOW_MS\n        };\n        rateLimitStore.set(clientIP, rateLimitData);\n        return null; // Allow request\n    }\n    rateLimitData.count++;\n    if (rateLimitData.count > RATE_LIMIT_MAX_REQUESTS) {\n        // Log rate limit violation\n        (0,_lib_audit__WEBPACK_IMPORTED_MODULE_2__.auditLog)({\n            action: \"RATE_LIMIT_EXCEEDED\",\n            entity: \"API\",\n            entityId: \"rate-limit\",\n            ipAddress: clientIP,\n            userAgent: request.headers.get(\"user-agent\") || \"unknown\",\n            details: {\n                requestCount: rateLimitData.count\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Rate limit exceeded\",\n            message: \"Too many requests. Please try again later.\",\n            retryAfter: Math.ceil((rateLimitData.resetTime - now) / 1000)\n        }, {\n            status: 429,\n            headers: {\n                \"Retry-After\": Math.ceil((rateLimitData.resetTime - now) / 1000).toString(),\n                \"X-RateLimit-Limit\": RATE_LIMIT_MAX_REQUESTS.toString(),\n                \"X-RateLimit-Remaining\": \"0\",\n                \"X-RateLimit-Reset\": rateLimitData.resetTime.toString()\n            }\n        });\n    }\n    return null; // Allow request\n}\n// Authentication middleware\nfunction withAuth(handler, options = {}) {\n    return async (request)=>{\n        try {\n            // Apply rate limiting\n            const rateLimitResponse = rateLimit(request);\n            if (rateLimitResponse) {\n                return rateLimitResponse;\n            }\n            // Verify authentication\n            const user = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.requireAuth)(request);\n            if (!user) {\n                (0,_lib_audit__WEBPACK_IMPORTED_MODULE_2__.auditLog)({\n                    action: \"UNAUTHORIZED_ACCESS_ATTEMPT\",\n                    entity: \"API\",\n                    entityId: request.nextUrl.pathname,\n                    ipAddress: request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\",\n                    userAgent: request.headers.get(\"user-agent\") || \"unknown\",\n                    details: {\n                        path: request.nextUrl.pathname,\n                        method: request.method\n                    }\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Unauthorized\",\n                    message: \"Authentication required\"\n                }, {\n                    status: 401\n                });\n            }\n            // Check required permissions\n            if (options.requiredPermissions) {\n                const hasRequiredPermissions = options.requiredPermissions.every((permission)=>(0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.hasPermission)(user, permission));\n                if (!hasRequiredPermissions) {\n                    (0,_lib_audit__WEBPACK_IMPORTED_MODULE_2__.auditLog)({\n                        userId: user.id,\n                        action: \"INSUFFICIENT_PERMISSIONS\",\n                        entity: \"API\",\n                        entityId: request.nextUrl.pathname,\n                        ipAddress: request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\",\n                        userAgent: request.headers.get(\"user-agent\") || \"unknown\",\n                        details: {\n                            requiredPermissions: options.requiredPermissions,\n                            userPermissions: user.permissions,\n                            path: request.nextUrl.pathname,\n                            method: request.method\n                        }\n                    });\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Forbidden\",\n                        message: \"Insufficient permissions\"\n                    }, {\n                        status: 403\n                    });\n                }\n            }\n            // Check allowed roles\n            if (options.allowedRoles && !options.allowedRoles.includes(user.role)) {\n                (0,_lib_audit__WEBPACK_IMPORTED_MODULE_2__.auditLog)({\n                    userId: user.id,\n                    action: \"ROLE_ACCESS_DENIED\",\n                    entity: \"API\",\n                    entityId: request.nextUrl.pathname,\n                    ipAddress: request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\",\n                    userAgent: request.headers.get(\"user-agent\") || \"unknown\",\n                    details: {\n                        allowedRoles: options.allowedRoles,\n                        userRole: user.role,\n                        path: request.nextUrl.pathname,\n                        method: request.method\n                    }\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Forbidden\",\n                    message: \"Role not authorized for this resource\"\n                }, {\n                    status: 403\n                });\n            }\n            // Add user to request\n            const authenticatedRequest = request;\n            authenticatedRequest.user = user;\n            // Log successful API access\n            (0,_lib_audit__WEBPACK_IMPORTED_MODULE_2__.auditLog)({\n                userId: user.id,\n                action: \"API_ACCESS\",\n                entity: \"API\",\n                entityId: request.nextUrl.pathname,\n                ipAddress: request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\",\n                userAgent: request.headers.get(\"user-agent\") || \"unknown\",\n                details: {\n                    path: request.nextUrl.pathname,\n                    method: request.method\n                }\n            });\n            return await handler(authenticatedRequest);\n        } catch (error) {\n            console.error(\"Auth middleware error:\", error);\n            (0,_lib_audit__WEBPACK_IMPORTED_MODULE_2__.auditLog)({\n                action: \"AUTH_MIDDLEWARE_ERROR\",\n                entity: \"API\",\n                entityId: request.nextUrl.pathname,\n                ipAddress: request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\",\n                userAgent: request.headers.get(\"user-agent\") || \"unknown\",\n                details: {\n                    error: error instanceof Error ? error.message : \"Unknown error\"\n                }\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Internal Server Error\",\n                message: \"Authentication failed\"\n            }, {\n                status: 500\n            });\n        }\n    };\n}\n// Input validation middleware\nfunction validateInput(schema) {\n    return (handler)=>{\n        return async (request)=>{\n            try {\n                let data;\n                if (request.method === \"GET\") {\n                    // Validate query parameters\n                    const searchParams = Object.fromEntries(request.nextUrl.searchParams.entries());\n                    data = schema.parse(searchParams);\n                } else {\n                    // Validate request body\n                    const body = await request.json();\n                    data = schema.parse(body);\n                }\n                return await handler(request, data);\n            } catch (error) {\n                (0,_lib_audit__WEBPACK_IMPORTED_MODULE_2__.auditLog)({\n                    userId: request.user?.id,\n                    action: \"VALIDATION_ERROR\",\n                    entity: \"API\",\n                    entityId: request.nextUrl.pathname,\n                    ipAddress: request.ip || request.headers.get(\"x-forwarded-for\") || \"unknown\",\n                    userAgent: request.headers.get(\"user-agent\") || \"unknown\",\n                    details: {\n                        error: error instanceof Error ? error.message : \"Validation failed\"\n                    }\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Validation Error\",\n                    message: \"Invalid input data\",\n                    details: error instanceof Error ? error.message : \"Validation failed\"\n                }, {\n                    status: 400\n                });\n            }\n        };\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbWlkZGxld2FyZS9hdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUF3RDtBQUNNO0FBQ3ZCO0FBTXZDLGlEQUFpRDtBQUNqRCxNQUFNSSxpQkFBaUIsSUFBSUM7QUFFM0IsOEJBQThCO0FBQzlCLE1BQU1DLHVCQUF1QkMsU0FBU0MsUUFBUUMsR0FBRyxDQUFDSCxvQkFBb0IsSUFBSSxXQUFXLGFBQWE7QUFDbEcsTUFBTUksMEJBQTBCSCxTQUFTQyxRQUFRQyxHQUFHLENBQUNDLHVCQUF1QixJQUFJO0FBRWhGLDJCQUEyQjtBQUNwQixTQUFTQyxVQUFVQyxPQUFvQjtJQUM1QyxNQUFNQyxXQUFXRCxRQUFRRSxFQUFFLElBQUlGLFFBQVFHLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNCQUFzQjtJQUN6RSxNQUFNQyxNQUFNQyxLQUFLRCxHQUFHO0lBRXBCLDJCQUEyQjtJQUMzQixLQUFLLE1BQU0sQ0FBQ0gsSUFBSUssS0FBSyxJQUFJZixlQUFlZ0IsT0FBTyxHQUFJO1FBQ2pELElBQUlILE1BQU1FLEtBQUtFLFNBQVMsRUFBRTtZQUN4QmpCLGVBQWVrQixNQUFNLENBQUNSO1FBQ3hCO0lBQ0Y7SUFFQSw0Q0FBNEM7SUFDNUMsSUFBSVMsZ0JBQWdCbkIsZUFBZVksR0FBRyxDQUFDSDtJQUV2QyxJQUFJLENBQUNVLGlCQUFpQk4sTUFBTU0sY0FBY0YsU0FBUyxFQUFFO1FBQ25ERSxnQkFBZ0I7WUFDZEMsT0FBTztZQUNQSCxXQUFXSixNQUFNWDtRQUNuQjtRQUNBRixlQUFlcUIsR0FBRyxDQUFDWixVQUFVVTtRQUM3QixPQUFPLE1BQU0sZ0JBQWdCO0lBQy9CO0lBRUFBLGNBQWNDLEtBQUs7SUFFbkIsSUFBSUQsY0FBY0MsS0FBSyxHQUFHZCx5QkFBeUI7UUFDakQsMkJBQTJCO1FBQzNCUCxvREFBUUEsQ0FBQztZQUNQdUIsUUFBUTtZQUNSQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsV0FBV2hCO1lBQ1hpQixXQUFXbEIsUUFBUUcsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUJBQWlCO1lBQ2hEZSxTQUFTO2dCQUFFQyxjQUFjVCxjQUFjQyxLQUFLO1lBQUM7UUFDL0M7UUFFQSxPQUFPeEIscURBQVlBLENBQUNpQyxJQUFJLENBQ3RCO1lBQ0VDLE9BQU87WUFDUEMsU0FBUztZQUNUQyxZQUFZQyxLQUFLQyxJQUFJLENBQUMsQ0FBQ2YsY0FBY0YsU0FBUyxHQUFHSixHQUFFLElBQUs7UUFDMUQsR0FDQTtZQUNFc0IsUUFBUTtZQUNSeEIsU0FBUztnQkFDUCxlQUFlc0IsS0FBS0MsSUFBSSxDQUFDLENBQUNmLGNBQWNGLFNBQVMsR0FBR0osR0FBRSxJQUFLLE1BQU11QixRQUFRO2dCQUN6RSxxQkFBcUI5Qix3QkFBd0I4QixRQUFRO2dCQUNyRCx5QkFBeUI7Z0JBQ3pCLHFCQUFxQmpCLGNBQWNGLFNBQVMsQ0FBQ21CLFFBQVE7WUFDdkQ7UUFDRjtJQUVKO0lBRUEsT0FBTyxNQUFNLGdCQUFnQjtBQUMvQjtBQUVBLDRCQUE0QjtBQUNyQixTQUFTQyxTQUNkQyxPQUFpRSxFQUNqRUMsVUFHSSxDQUFDLENBQUM7SUFFTixPQUFPLE9BQU8vQjtRQUNaLElBQUk7WUFDRixzQkFBc0I7WUFDdEIsTUFBTWdDLG9CQUFvQmpDLFVBQVVDO1lBQ3BDLElBQUlnQyxtQkFBbUI7Z0JBQ3JCLE9BQU9BO1lBQ1Q7WUFFQSx3QkFBd0I7WUFDeEIsTUFBTUMsT0FBTzVDLHNEQUFXQSxDQUFDVztZQUV6QixJQUFJLENBQUNpQyxNQUFNO2dCQUNUMUMsb0RBQVFBLENBQUM7b0JBQ1B1QixRQUFRO29CQUNSQyxRQUFRO29CQUNSQyxVQUFVaEIsUUFBUWtDLE9BQU8sQ0FBQ0MsUUFBUTtvQkFDbENsQixXQUFXakIsUUFBUUUsRUFBRSxJQUFJRixRQUFRRyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxzQkFBc0I7b0JBQ25FYyxXQUFXbEIsUUFBUUcsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUJBQWlCO29CQUNoRGUsU0FBUzt3QkFBRWlCLE1BQU1wQyxRQUFRa0MsT0FBTyxDQUFDQyxRQUFRO3dCQUFFRSxRQUFRckMsUUFBUXFDLE1BQU07b0JBQUM7Z0JBQ3BFO2dCQUVBLE9BQU9qRCxxREFBWUEsQ0FBQ2lDLElBQUksQ0FDdEI7b0JBQUVDLE9BQU87b0JBQWdCQyxTQUFTO2dCQUEwQixHQUM1RDtvQkFBRUksUUFBUTtnQkFBSTtZQUVsQjtZQUVBLDZCQUE2QjtZQUM3QixJQUFJSSxRQUFRTyxtQkFBbUIsRUFBRTtnQkFDL0IsTUFBTUMseUJBQXlCUixRQUFRTyxtQkFBbUIsQ0FBQ0UsS0FBSyxDQUFDQyxDQUFBQSxhQUMvRG5ELHdEQUFhQSxDQUFDMkMsTUFBTVE7Z0JBR3RCLElBQUksQ0FBQ0Ysd0JBQXdCO29CQUMzQmhELG9EQUFRQSxDQUFDO3dCQUNQbUQsUUFBUVQsS0FBS1UsRUFBRTt3QkFDZjdCLFFBQVE7d0JBQ1JDLFFBQVE7d0JBQ1JDLFVBQVVoQixRQUFRa0MsT0FBTyxDQUFDQyxRQUFRO3dCQUNsQ2xCLFdBQVdqQixRQUFRRSxFQUFFLElBQUlGLFFBQVFHLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNCQUFzQjt3QkFDbkVjLFdBQVdsQixRQUFRRyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxpQkFBaUI7d0JBQ2hEZSxTQUFTOzRCQUNQbUIscUJBQXFCUCxRQUFRTyxtQkFBbUI7NEJBQ2hETSxpQkFBaUJYLEtBQUtZLFdBQVc7NEJBQ2pDVCxNQUFNcEMsUUFBUWtDLE9BQU8sQ0FBQ0MsUUFBUTs0QkFDOUJFLFFBQVFyQyxRQUFRcUMsTUFBTTt3QkFDeEI7b0JBQ0Y7b0JBRUEsT0FBT2pELHFEQUFZQSxDQUFDaUMsSUFBSSxDQUN0Qjt3QkFBRUMsT0FBTzt3QkFBYUMsU0FBUztvQkFBMkIsR0FDMUQ7d0JBQUVJLFFBQVE7b0JBQUk7Z0JBRWxCO1lBQ0Y7WUFFQSxzQkFBc0I7WUFDdEIsSUFBSUksUUFBUWUsWUFBWSxJQUFJLENBQUNmLFFBQVFlLFlBQVksQ0FBQ0MsUUFBUSxDQUFDZCxLQUFLZSxJQUFJLEdBQUc7Z0JBQ3JFekQsb0RBQVFBLENBQUM7b0JBQ1BtRCxRQUFRVCxLQUFLVSxFQUFFO29CQUNmN0IsUUFBUTtvQkFDUkMsUUFBUTtvQkFDUkMsVUFBVWhCLFFBQVFrQyxPQUFPLENBQUNDLFFBQVE7b0JBQ2xDbEIsV0FBV2pCLFFBQVFFLEVBQUUsSUFBSUYsUUFBUUcsT0FBTyxDQUFDQyxHQUFHLENBQUMsc0JBQXNCO29CQUNuRWMsV0FBV2xCLFFBQVFHLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGlCQUFpQjtvQkFDaERlLFNBQVM7d0JBQ1AyQixjQUFjZixRQUFRZSxZQUFZO3dCQUNsQ0csVUFBVWhCLEtBQUtlLElBQUk7d0JBQ25CWixNQUFNcEMsUUFBUWtDLE9BQU8sQ0FBQ0MsUUFBUTt3QkFDOUJFLFFBQVFyQyxRQUFRcUMsTUFBTTtvQkFDeEI7Z0JBQ0Y7Z0JBRUEsT0FBT2pELHFEQUFZQSxDQUFDaUMsSUFBSSxDQUN0QjtvQkFBRUMsT0FBTztvQkFBYUMsU0FBUztnQkFBd0MsR0FDdkU7b0JBQUVJLFFBQVE7Z0JBQUk7WUFFbEI7WUFFQSxzQkFBc0I7WUFDdEIsTUFBTXVCLHVCQUF1QmxEO1lBQzdCa0QscUJBQXFCakIsSUFBSSxHQUFHQTtZQUU1Qiw0QkFBNEI7WUFDNUIxQyxvREFBUUEsQ0FBQztnQkFDUG1ELFFBQVFULEtBQUtVLEVBQUU7Z0JBQ2Y3QixRQUFRO2dCQUNSQyxRQUFRO2dCQUNSQyxVQUFVaEIsUUFBUWtDLE9BQU8sQ0FBQ0MsUUFBUTtnQkFDbENsQixXQUFXakIsUUFBUUUsRUFBRSxJQUFJRixRQUFRRyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxzQkFBc0I7Z0JBQ25FYyxXQUFXbEIsUUFBUUcsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUJBQWlCO2dCQUNoRGUsU0FBUztvQkFBRWlCLE1BQU1wQyxRQUFRa0MsT0FBTyxDQUFDQyxRQUFRO29CQUFFRSxRQUFRckMsUUFBUXFDLE1BQU07Z0JBQUM7WUFDcEU7WUFFQSxPQUFPLE1BQU1QLFFBQVFvQjtRQUV2QixFQUFFLE9BQU81QixPQUFPO1lBQ2Q2QixRQUFRN0IsS0FBSyxDQUFDLDBCQUEwQkE7WUFFeEMvQixvREFBUUEsQ0FBQztnQkFDUHVCLFFBQVE7Z0JBQ1JDLFFBQVE7Z0JBQ1JDLFVBQVVoQixRQUFRa0MsT0FBTyxDQUFDQyxRQUFRO2dCQUNsQ2xCLFdBQVdqQixRQUFRRSxFQUFFLElBQUlGLFFBQVFHLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNCQUFzQjtnQkFDbkVjLFdBQVdsQixRQUFRRyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxpQkFBaUI7Z0JBQ2hEZSxTQUFTO29CQUFFRyxPQUFPQSxpQkFBaUI4QixRQUFROUIsTUFBTUMsT0FBTyxHQUFHO2dCQUFnQjtZQUM3RTtZQUVBLE9BQU9uQyxxREFBWUEsQ0FBQ2lDLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87Z0JBQXlCQyxTQUFTO1lBQXdCLEdBQ25FO2dCQUFFSSxRQUFRO1lBQUk7UUFFbEI7SUFDRjtBQUNGO0FBRUEsOEJBQThCO0FBQ3ZCLFNBQVMwQixjQUFpQkMsTUFBVztJQUMxQyxPQUFPLENBQUN4QjtRQUNOLE9BQU8sT0FBTzlCO1lBQ1osSUFBSTtnQkFDRixJQUFJTztnQkFFSixJQUFJUCxRQUFRcUMsTUFBTSxLQUFLLE9BQU87b0JBQzVCLDRCQUE0QjtvQkFDNUIsTUFBTWtCLGVBQWVDLE9BQU9DLFdBQVcsQ0FBQ3pELFFBQVFrQyxPQUFPLENBQUNxQixZQUFZLENBQUMvQyxPQUFPO29CQUM1RUQsT0FBTytDLE9BQU9JLEtBQUssQ0FBQ0g7Z0JBQ3RCLE9BQU87b0JBQ0wsd0JBQXdCO29CQUN4QixNQUFNSSxPQUFPLE1BQU0zRCxRQUFRcUIsSUFBSTtvQkFDL0JkLE9BQU8rQyxPQUFPSSxLQUFLLENBQUNDO2dCQUN0QjtnQkFFQSxPQUFPLE1BQU03QixRQUFROUIsU0FBU087WUFFaEMsRUFBRSxPQUFPZSxPQUFPO2dCQUNkL0Isb0RBQVFBLENBQUM7b0JBQ1BtRCxRQUFRMUMsUUFBUWlDLElBQUksRUFBRVU7b0JBQ3RCN0IsUUFBUTtvQkFDUkMsUUFBUTtvQkFDUkMsVUFBVWhCLFFBQVFrQyxPQUFPLENBQUNDLFFBQVE7b0JBQ2xDbEIsV0FBV2pCLFFBQVFFLEVBQUUsSUFBSUYsUUFBUUcsT0FBTyxDQUFDQyxHQUFHLENBQUMsc0JBQXNCO29CQUNuRWMsV0FBV2xCLFFBQVFHLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGlCQUFpQjtvQkFDaERlLFNBQVM7d0JBQUVHLE9BQU9BLGlCQUFpQjhCLFFBQVE5QixNQUFNQyxPQUFPLEdBQUc7b0JBQW9CO2dCQUNqRjtnQkFFQSxPQUFPbkMscURBQVlBLENBQUNpQyxJQUFJLENBQ3RCO29CQUNFQyxPQUFPO29CQUNQQyxTQUFTO29CQUNUSixTQUFTRyxpQkFBaUI4QixRQUFROUIsTUFBTUMsT0FBTyxHQUFHO2dCQUNwRCxHQUNBO29CQUFFSSxRQUFRO2dCQUFJO1lBRWxCO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuaXplLW5leHQtZnJlZS8uL3NyYy9taWRkbGV3YXJlL2F1dGgudHM/YjlhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgcmVxdWlyZUF1dGgsIGhhc1Blcm1pc3Npb24sIFVzZXIgfSBmcm9tICdAL2xpYi9hdXRoJztcbmltcG9ydCB7IGF1ZGl0TG9nIH0gZnJvbSAnQC9saWIvYXVkaXQnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEF1dGhlbnRpY2F0ZWRSZXF1ZXN0IGV4dGVuZHMgTmV4dFJlcXVlc3Qge1xuICB1c2VyPzogVXNlcjtcbn1cblxuLy8gUmF0ZSBsaW1pdGluZyBzdG9yZSAoaW4gcHJvZHVjdGlvbiwgdXNlIFJlZGlzKVxuY29uc3QgcmF0ZUxpbWl0U3RvcmUgPSBuZXcgTWFwPHN0cmluZywgeyBjb3VudDogbnVtYmVyOyByZXNldFRpbWU6IG51bWJlciB9PigpO1xuXG4vLyBSYXRlIGxpbWl0aW5nIGNvbmZpZ3VyYXRpb25cbmNvbnN0IFJBVEVfTElNSVRfV0lORE9XX01TID0gcGFyc2VJbnQocHJvY2Vzcy5lbnYuUkFURV9MSU1JVF9XSU5ET1dfTVMgfHwgJzkwMDAwMCcpOyAvLyAxNSBtaW51dGVzXG5jb25zdCBSQVRFX0xJTUlUX01BWF9SRVFVRVNUUyA9IHBhcnNlSW50KHByb2Nlc3MuZW52LlJBVEVfTElNSVRfTUFYX1JFUVVFU1RTIHx8ICcxMDAnKTtcblxuLy8gUmF0ZSBsaW1pdGluZyBtaWRkbGV3YXJlXG5leHBvcnQgZnVuY3Rpb24gcmF0ZUxpbWl0KHJlcXVlc3Q6IE5leHRSZXF1ZXN0KTogTmV4dFJlc3BvbnNlIHwgbnVsbCB7XG4gIGNvbnN0IGNsaWVudElQID0gcmVxdWVzdC5pcCB8fCByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd4LWZvcndhcmRlZC1mb3InKSB8fCAndW5rbm93bic7XG4gIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gIFxuICAvLyBDbGVhbiB1cCBleHBpcmVkIGVudHJpZXNcbiAgZm9yIChjb25zdCBbaXAsIGRhdGFdIG9mIHJhdGVMaW1pdFN0b3JlLmVudHJpZXMoKSkge1xuICAgIGlmIChub3cgPiBkYXRhLnJlc2V0VGltZSkge1xuICAgICAgcmF0ZUxpbWl0U3RvcmUuZGVsZXRlKGlwKTtcbiAgICB9XG4gIH1cbiAgXG4gIC8vIEdldCBvciBjcmVhdGUgcmF0ZSBsaW1pdCBkYXRhIGZvciB0aGlzIElQXG4gIGxldCByYXRlTGltaXREYXRhID0gcmF0ZUxpbWl0U3RvcmUuZ2V0KGNsaWVudElQKTtcbiAgXG4gIGlmICghcmF0ZUxpbWl0RGF0YSB8fCBub3cgPiByYXRlTGltaXREYXRhLnJlc2V0VGltZSkge1xuICAgIHJhdGVMaW1pdERhdGEgPSB7XG4gICAgICBjb3VudDogMSxcbiAgICAgIHJlc2V0VGltZTogbm93ICsgUkFURV9MSU1JVF9XSU5ET1dfTVNcbiAgICB9O1xuICAgIHJhdGVMaW1pdFN0b3JlLnNldChjbGllbnRJUCwgcmF0ZUxpbWl0RGF0YSk7XG4gICAgcmV0dXJuIG51bGw7IC8vIEFsbG93IHJlcXVlc3RcbiAgfVxuICBcbiAgcmF0ZUxpbWl0RGF0YS5jb3VudCsrO1xuICBcbiAgaWYgKHJhdGVMaW1pdERhdGEuY291bnQgPiBSQVRFX0xJTUlUX01BWF9SRVFVRVNUUykge1xuICAgIC8vIExvZyByYXRlIGxpbWl0IHZpb2xhdGlvblxuICAgIGF1ZGl0TG9nKHtcbiAgICAgIGFjdGlvbjogJ1JBVEVfTElNSVRfRVhDRUVERUQnLFxuICAgICAgZW50aXR5OiAnQVBJJyxcbiAgICAgIGVudGl0eUlkOiAncmF0ZS1saW1pdCcsXG4gICAgICBpcEFkZHJlc3M6IGNsaWVudElQLFxuICAgICAgdXNlckFnZW50OiByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd1c2VyLWFnZW50JykgfHwgJ3Vua25vd24nLFxuICAgICAgZGV0YWlsczogeyByZXF1ZXN0Q291bnQ6IHJhdGVMaW1pdERhdGEuY291bnQgfVxuICAgIH0pO1xuICAgIFxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgXG4gICAgICAgIGVycm9yOiAnUmF0ZSBsaW1pdCBleGNlZWRlZCcsXG4gICAgICAgIG1lc3NhZ2U6ICdUb28gbWFueSByZXF1ZXN0cy4gUGxlYXNlIHRyeSBhZ2FpbiBsYXRlci4nLFxuICAgICAgICByZXRyeUFmdGVyOiBNYXRoLmNlaWwoKHJhdGVMaW1pdERhdGEucmVzZXRUaW1lIC0gbm93KSAvIDEwMDApXG4gICAgICB9LFxuICAgICAgeyBcbiAgICAgICAgc3RhdHVzOiA0MjksXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnUmV0cnktQWZ0ZXInOiBNYXRoLmNlaWwoKHJhdGVMaW1pdERhdGEucmVzZXRUaW1lIC0gbm93KSAvIDEwMDApLnRvU3RyaW5nKCksXG4gICAgICAgICAgJ1gtUmF0ZUxpbWl0LUxpbWl0JzogUkFURV9MSU1JVF9NQVhfUkVRVUVTVFMudG9TdHJpbmcoKSxcbiAgICAgICAgICAnWC1SYXRlTGltaXQtUmVtYWluaW5nJzogJzAnLFxuICAgICAgICAgICdYLVJhdGVMaW1pdC1SZXNldCc6IHJhdGVMaW1pdERhdGEucmVzZXRUaW1lLnRvU3RyaW5nKClcbiAgICAgICAgfVxuICAgICAgfVxuICAgICk7XG4gIH1cbiAgXG4gIHJldHVybiBudWxsOyAvLyBBbGxvdyByZXF1ZXN0XG59XG5cbi8vIEF1dGhlbnRpY2F0aW9uIG1pZGRsZXdhcmVcbmV4cG9ydCBmdW5jdGlvbiB3aXRoQXV0aChcbiAgaGFuZGxlcjogKHJlcXVlc3Q6IEF1dGhlbnRpY2F0ZWRSZXF1ZXN0KSA9PiBQcm9taXNlPE5leHRSZXNwb25zZT4sXG4gIG9wdGlvbnM6IHtcbiAgICByZXF1aXJlZFBlcm1pc3Npb25zPzogc3RyaW5nW107XG4gICAgYWxsb3dlZFJvbGVzPzogc3RyaW5nW107XG4gIH0gPSB7fVxuKSB7XG4gIHJldHVybiBhc3luYyAocmVxdWVzdDogTmV4dFJlcXVlc3QpOiBQcm9taXNlPE5leHRSZXNwb25zZT4gPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBBcHBseSByYXRlIGxpbWl0aW5nXG4gICAgICBjb25zdCByYXRlTGltaXRSZXNwb25zZSA9IHJhdGVMaW1pdChyZXF1ZXN0KTtcbiAgICAgIGlmIChyYXRlTGltaXRSZXNwb25zZSkge1xuICAgICAgICByZXR1cm4gcmF0ZUxpbWl0UmVzcG9uc2U7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIFZlcmlmeSBhdXRoZW50aWNhdGlvblxuICAgICAgY29uc3QgdXNlciA9IHJlcXVpcmVBdXRoKHJlcXVlc3QpO1xuICAgICAgXG4gICAgICBpZiAoIXVzZXIpIHtcbiAgICAgICAgYXVkaXRMb2coe1xuICAgICAgICAgIGFjdGlvbjogJ1VOQVVUSE9SSVpFRF9BQ0NFU1NfQVRURU1QVCcsXG4gICAgICAgICAgZW50aXR5OiAnQVBJJyxcbiAgICAgICAgICBlbnRpdHlJZDogcmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lLFxuICAgICAgICAgIGlwQWRkcmVzczogcmVxdWVzdC5pcCB8fCByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd4LWZvcndhcmRlZC1mb3InKSB8fCAndW5rbm93bicsXG4gICAgICAgICAgdXNlckFnZW50OiByZXF1ZXN0LmhlYWRlcnMuZ2V0KCd1c2VyLWFnZW50JykgfHwgJ3Vua25vd24nLFxuICAgICAgICAgIGRldGFpbHM6IHsgcGF0aDogcmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lLCBtZXRob2Q6IHJlcXVlc3QubWV0aG9kIH1cbiAgICAgICAgfSk7XG4gICAgICAgIFxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBlcnJvcjogJ1VuYXV0aG9yaXplZCcsIG1lc3NhZ2U6ICdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAxIH1cbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gQ2hlY2sgcmVxdWlyZWQgcGVybWlzc2lvbnNcbiAgICAgIGlmIChvcHRpb25zLnJlcXVpcmVkUGVybWlzc2lvbnMpIHtcbiAgICAgICAgY29uc3QgaGFzUmVxdWlyZWRQZXJtaXNzaW9ucyA9IG9wdGlvbnMucmVxdWlyZWRQZXJtaXNzaW9ucy5ldmVyeShwZXJtaXNzaW9uID0+XG4gICAgICAgICAgaGFzUGVybWlzc2lvbih1c2VyLCBwZXJtaXNzaW9uKVxuICAgICAgICApO1xuICAgICAgICBcbiAgICAgICAgaWYgKCFoYXNSZXF1aXJlZFBlcm1pc3Npb25zKSB7XG4gICAgICAgICAgYXVkaXRMb2coe1xuICAgICAgICAgICAgdXNlcklkOiB1c2VyLmlkLFxuICAgICAgICAgICAgYWN0aW9uOiAnSU5TVUZGSUNJRU5UX1BFUk1JU1NJT05TJyxcbiAgICAgICAgICAgIGVudGl0eTogJ0FQSScsXG4gICAgICAgICAgICBlbnRpdHlJZDogcmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lLFxuICAgICAgICAgICAgaXBBZGRyZXNzOiByZXF1ZXN0LmlwIHx8IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3gtZm9yd2FyZGVkLWZvcicpIHx8ICd1bmtub3duJyxcbiAgICAgICAgICAgIHVzZXJBZ2VudDogcmVxdWVzdC5oZWFkZXJzLmdldCgndXNlci1hZ2VudCcpIHx8ICd1bmtub3duJyxcbiAgICAgICAgICAgIGRldGFpbHM6IHsgXG4gICAgICAgICAgICAgIHJlcXVpcmVkUGVybWlzc2lvbnM6IG9wdGlvbnMucmVxdWlyZWRQZXJtaXNzaW9ucyxcbiAgICAgICAgICAgICAgdXNlclBlcm1pc3Npb25zOiB1c2VyLnBlcm1pc3Npb25zLFxuICAgICAgICAgICAgICBwYXRoOiByZXF1ZXN0Lm5leHRVcmwucGF0aG5hbWUsXG4gICAgICAgICAgICAgIG1ldGhvZDogcmVxdWVzdC5tZXRob2RcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgICBcbiAgICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgICB7IGVycm9yOiAnRm9yYmlkZGVuJywgbWVzc2FnZTogJ0luc3VmZmljaWVudCBwZXJtaXNzaW9ucycgfSxcbiAgICAgICAgICAgIHsgc3RhdHVzOiA0MDMgfVxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gQ2hlY2sgYWxsb3dlZCByb2xlc1xuICAgICAgaWYgKG9wdGlvbnMuYWxsb3dlZFJvbGVzICYmICFvcHRpb25zLmFsbG93ZWRSb2xlcy5pbmNsdWRlcyh1c2VyLnJvbGUpKSB7XG4gICAgICAgIGF1ZGl0TG9nKHtcbiAgICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXG4gICAgICAgICAgYWN0aW9uOiAnUk9MRV9BQ0NFU1NfREVOSUVEJyxcbiAgICAgICAgICBlbnRpdHk6ICdBUEknLFxuICAgICAgICAgIGVudGl0eUlkOiByZXF1ZXN0Lm5leHRVcmwucGF0aG5hbWUsXG4gICAgICAgICAgaXBBZGRyZXNzOiByZXF1ZXN0LmlwIHx8IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3gtZm9yd2FyZGVkLWZvcicpIHx8ICd1bmtub3duJyxcbiAgICAgICAgICB1c2VyQWdlbnQ6IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3VzZXItYWdlbnQnKSB8fCAndW5rbm93bicsXG4gICAgICAgICAgZGV0YWlsczogeyBcbiAgICAgICAgICAgIGFsbG93ZWRSb2xlczogb3B0aW9ucy5hbGxvd2VkUm9sZXMsXG4gICAgICAgICAgICB1c2VyUm9sZTogdXNlci5yb2xlLFxuICAgICAgICAgICAgcGF0aDogcmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lLFxuICAgICAgICAgICAgbWV0aG9kOiByZXF1ZXN0Lm1ldGhvZFxuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIFxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBlcnJvcjogJ0ZvcmJpZGRlbicsIG1lc3NhZ2U6ICdSb2xlIG5vdCBhdXRob3JpemVkIGZvciB0aGlzIHJlc291cmNlJyB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MDMgfVxuICAgICAgICApO1xuICAgICAgfVxuICAgICAgXG4gICAgICAvLyBBZGQgdXNlciB0byByZXF1ZXN0XG4gICAgICBjb25zdCBhdXRoZW50aWNhdGVkUmVxdWVzdCA9IHJlcXVlc3QgYXMgQXV0aGVudGljYXRlZFJlcXVlc3Q7XG4gICAgICBhdXRoZW50aWNhdGVkUmVxdWVzdC51c2VyID0gdXNlcjtcbiAgICAgIFxuICAgICAgLy8gTG9nIHN1Y2Nlc3NmdWwgQVBJIGFjY2Vzc1xuICAgICAgYXVkaXRMb2coe1xuICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXG4gICAgICAgIGFjdGlvbjogJ0FQSV9BQ0NFU1MnLFxuICAgICAgICBlbnRpdHk6ICdBUEknLFxuICAgICAgICBlbnRpdHlJZDogcmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lLFxuICAgICAgICBpcEFkZHJlc3M6IHJlcXVlc3QuaXAgfHwgcmVxdWVzdC5oZWFkZXJzLmdldCgneC1mb3J3YXJkZWQtZm9yJykgfHwgJ3Vua25vd24nLFxuICAgICAgICB1c2VyQWdlbnQ6IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3VzZXItYWdlbnQnKSB8fCAndW5rbm93bicsXG4gICAgICAgIGRldGFpbHM6IHsgcGF0aDogcmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lLCBtZXRob2Q6IHJlcXVlc3QubWV0aG9kIH1cbiAgICAgIH0pO1xuICAgICAgXG4gICAgICByZXR1cm4gYXdhaXQgaGFuZGxlcihhdXRoZW50aWNhdGVkUmVxdWVzdCk7XG4gICAgICBcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignQXV0aCBtaWRkbGV3YXJlIGVycm9yOicsIGVycm9yKTtcbiAgICAgIFxuICAgICAgYXVkaXRMb2coe1xuICAgICAgICBhY3Rpb246ICdBVVRIX01JRERMRVdBUkVfRVJST1InLFxuICAgICAgICBlbnRpdHk6ICdBUEknLFxuICAgICAgICBlbnRpdHlJZDogcmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lLFxuICAgICAgICBpcEFkZHJlc3M6IHJlcXVlc3QuaXAgfHwgcmVxdWVzdC5oZWFkZXJzLmdldCgneC1mb3J3YXJkZWQtZm9yJykgfHwgJ3Vua25vd24nLFxuICAgICAgICB1c2VyQWdlbnQ6IHJlcXVlc3QuaGVhZGVycy5nZXQoJ3VzZXItYWdlbnQnKSB8fCAndW5rbm93bicsXG4gICAgICAgIGRldGFpbHM6IHsgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InIH1cbiAgICAgIH0pO1xuICAgICAgXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdJbnRlcm5hbCBTZXJ2ZXIgRXJyb3InLCBtZXNzYWdlOiAnQXV0aGVudGljYXRpb24gZmFpbGVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICk7XG4gICAgfVxuICB9O1xufVxuXG4vLyBJbnB1dCB2YWxpZGF0aW9uIG1pZGRsZXdhcmVcbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZUlucHV0PFQ+KHNjaGVtYTogYW55KSB7XG4gIHJldHVybiAoaGFuZGxlcjogKHJlcXVlc3Q6IEF1dGhlbnRpY2F0ZWRSZXF1ZXN0LCB2YWxpZGF0ZWREYXRhOiBUKSA9PiBQcm9taXNlPE5leHRSZXNwb25zZT4pID0+IHtcbiAgICByZXR1cm4gYXN5bmMgKHJlcXVlc3Q6IEF1dGhlbnRpY2F0ZWRSZXF1ZXN0KTogUHJvbWlzZTxOZXh0UmVzcG9uc2U+ID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGxldCBkYXRhOiBhbnk7XG4gICAgICAgIFxuICAgICAgICBpZiAocmVxdWVzdC5tZXRob2QgPT09ICdHRVQnKSB7XG4gICAgICAgICAgLy8gVmFsaWRhdGUgcXVlcnkgcGFyYW1ldGVyc1xuICAgICAgICAgIGNvbnN0IHNlYXJjaFBhcmFtcyA9IE9iamVjdC5mcm9tRW50cmllcyhyZXF1ZXN0Lm5leHRVcmwuc2VhcmNoUGFyYW1zLmVudHJpZXMoKSk7XG4gICAgICAgICAgZGF0YSA9IHNjaGVtYS5wYXJzZShzZWFyY2hQYXJhbXMpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIFZhbGlkYXRlIHJlcXVlc3QgYm9keVxuICAgICAgICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICAgICAgICBkYXRhID0gc2NoZW1hLnBhcnNlKGJvZHkpO1xuICAgICAgICB9XG4gICAgICAgIFxuICAgICAgICByZXR1cm4gYXdhaXQgaGFuZGxlcihyZXF1ZXN0LCBkYXRhKTtcbiAgICAgICAgXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBhdWRpdExvZyh7XG4gICAgICAgICAgdXNlcklkOiByZXF1ZXN0LnVzZXI/LmlkLFxuICAgICAgICAgIGFjdGlvbjogJ1ZBTElEQVRJT05fRVJST1InLFxuICAgICAgICAgIGVudGl0eTogJ0FQSScsXG4gICAgICAgICAgZW50aXR5SWQ6IHJlcXVlc3QubmV4dFVybC5wYXRobmFtZSxcbiAgICAgICAgICBpcEFkZHJlc3M6IHJlcXVlc3QuaXAgfHwgcmVxdWVzdC5oZWFkZXJzLmdldCgneC1mb3J3YXJkZWQtZm9yJykgfHwgJ3Vua25vd24nLFxuICAgICAgICAgIHVzZXJBZ2VudDogcmVxdWVzdC5oZWFkZXJzLmdldCgndXNlci1hZ2VudCcpIHx8ICd1bmtub3duJyxcbiAgICAgICAgICBkZXRhaWxzOiB7IGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdWYWxpZGF0aW9uIGZhaWxlZCcgfVxuICAgICAgICB9KTtcbiAgICAgICAgXG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IFxuICAgICAgICAgICAgZXJyb3I6ICdWYWxpZGF0aW9uIEVycm9yJyxcbiAgICAgICAgICAgIG1lc3NhZ2U6ICdJbnZhbGlkIGlucHV0IGRhdGEnLFxuICAgICAgICAgICAgZGV0YWlsczogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVmFsaWRhdGlvbiBmYWlsZWQnXG4gICAgICAgICAgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICB9O1xuICB9O1xufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsInJlcXVpcmVBdXRoIiwiaGFzUGVybWlzc2lvbiIsImF1ZGl0TG9nIiwicmF0ZUxpbWl0U3RvcmUiLCJNYXAiLCJSQVRFX0xJTUlUX1dJTkRPV19NUyIsInBhcnNlSW50IiwicHJvY2VzcyIsImVudiIsIlJBVEVfTElNSVRfTUFYX1JFUVVFU1RTIiwicmF0ZUxpbWl0IiwicmVxdWVzdCIsImNsaWVudElQIiwiaXAiLCJoZWFkZXJzIiwiZ2V0Iiwibm93IiwiRGF0ZSIsImRhdGEiLCJlbnRyaWVzIiwicmVzZXRUaW1lIiwiZGVsZXRlIiwicmF0ZUxpbWl0RGF0YSIsImNvdW50Iiwic2V0IiwiYWN0aW9uIiwiZW50aXR5IiwiZW50aXR5SWQiLCJpcEFkZHJlc3MiLCJ1c2VyQWdlbnQiLCJkZXRhaWxzIiwicmVxdWVzdENvdW50IiwianNvbiIsImVycm9yIiwibWVzc2FnZSIsInJldHJ5QWZ0ZXIiLCJNYXRoIiwiY2VpbCIsInN0YXR1cyIsInRvU3RyaW5nIiwid2l0aEF1dGgiLCJoYW5kbGVyIiwib3B0aW9ucyIsInJhdGVMaW1pdFJlc3BvbnNlIiwidXNlciIsIm5leHRVcmwiLCJwYXRobmFtZSIsInBhdGgiLCJtZXRob2QiLCJyZXF1aXJlZFBlcm1pc3Npb25zIiwiaGFzUmVxdWlyZWRQZXJtaXNzaW9ucyIsImV2ZXJ5IiwicGVybWlzc2lvbiIsInVzZXJJZCIsImlkIiwidXNlclBlcm1pc3Npb25zIiwicGVybWlzc2lvbnMiLCJhbGxvd2VkUm9sZXMiLCJpbmNsdWRlcyIsInJvbGUiLCJ1c2VyUm9sZSIsImF1dGhlbnRpY2F0ZWRSZXF1ZXN0IiwiY29uc29sZSIsIkVycm9yIiwidmFsaWRhdGVJbnB1dCIsInNjaGVtYSIsInNlYXJjaFBhcmFtcyIsIk9iamVjdCIsImZyb21FbnRyaWVzIiwicGFyc2UiLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/middleware/auth.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/zod","vendor-chunks/jws","vendor-chunks/yallist","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/bcryptjs","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lru-cache","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CInnovative-Centre-Admin%5Cpackage%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CInnovative-Centre-Admin%5Cpackage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();