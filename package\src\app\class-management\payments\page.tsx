'use client';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Stack,
  Tabs,
  Tab,
  Select,
  FormControl,
  InputLabel,
  Autocomplete,
  InputAdornment,
  CircularProgress,
  Alert,
  Snackbar,
} from '@mui/material';
import { useState, useRef, ChangeEvent, useEffect } from 'react';
import { usePayments } from '@/hooks/usePayments';
import PageContainer from '@/app/(DashboardLayout)/components/container/PageContainer';
import { IconEdit, IconTrash, IconReceipt, IconFilter } from '@tabler/icons-react';
import { PaymentWithStudent } from '@/types/payment';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`payment-tabpanel-${index}`}
      aria-labelledby={`payment-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const Payments = () => {
  // Use the payments hook for state management
  const {
    payments,
    loading,
    error,
    updatePayment,
    createPayment,
    deletePayment,
  } = usePayments();

  // Local state
  const [students, setStudents] = useState<any[]>([]);
  const [selectedPayment, setSelectedPayment] = useState<PaymentWithStudent | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [selectedStudent, setSelectedStudent] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Form refs
  const [selectedPaymentStudent, setSelectedPaymentStudent] = useState<{
    id: string;
    name: string;
    phone: string;
  } | null>(null);
  const amountRef = useRef<HTMLInputElement>(null);
  const dateRef = useRef<HTMLInputElement>(null);
  const methodRef = useRef<HTMLInputElement>(null);
  const statusRef = useRef<HTMLInputElement>(null);
  const descriptionRef = useRef<HTMLInputElement>(null);

  // Fetch students data
  useEffect(() => {
    const fetchStudents = async () => {
      try {
        const response = await fetch('/api/students');
        if (!response.ok) throw new Error('Failed to fetch students');
        const data = await response.json();
        setStudents(data);
      } catch (err) {
        console.error('Failed to fetch students:', err);
      }
    };

    fetchStudents();
  }, []);

  // CRUD Functions
  const handleSavePayment = async () => {
    if (!selectedPaymentStudent) {
      setSnackbar({
        open: true,
        message: 'Please select a student',
        severity: 'error',
      });
      return;
    }

    try {
      const paymentData = {
        studentId: selectedPaymentStudent.id,
        amount: Number((amountRef.current as any)?.value),
        date: (dateRef.current as any)?.value,
        method: (methodRef.current as any)?.value as 'cash' | 'card' | 'transfer',
        status: (statusRef.current as any)?.value as 'paid' | 'unpaid',
        description: (descriptionRef.current as any)?.value,
      };

      if (selectedPayment) {
        await updatePayment({ ...paymentData, id: selectedPayment.id });
      } else {
        await createPayment(paymentData);
      }

      setSnackbar({
        open: true,
        message: `Payment ${selectedPayment ? 'updated' : 'created'} successfully`,
        severity: 'success',
      });
      handleCloseDialog();
    } catch (err) {
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to save payment',
        severity: 'error',
      });
    }
  };

  const handleDeletePayment = async (paymentId: string) => {
    try {
      await deletePayment(paymentId);
      setSnackbar({
        open: true,
        message: 'Payment deleted successfully',
        severity: 'success',
      });
    } catch (err) {
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to delete payment',
        severity: 'error',
      });
    }
    setIsDeleteDialogOpen(false);
  };

  // Filter functions
  const getFilteredPayments = () => {
    let filtered = payments;
    
    // Filter by selected student
    if (selectedStudent !== 'all') {
      filtered = filtered.filter(payment => payment.studentId === selectedStudent);
    }
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(payment => {
        return (
          payment.student.name.toLowerCase().includes(query) ||
          payment.description.toLowerCase().includes(query) ||
          payment.amount.toString().includes(query) ||
          payment.method.toLowerCase().includes(query) ||
          payment.status.toLowerCase().includes(query)
        );
      });
    }
    
    return filtered;
  };

  const getStudentPaymentStats = (studentId: string) => {
    const studentPayments = payments.filter(p => p.studentId === studentId);
    return {
      total: studentPayments.reduce((sum, p) => sum + p.amount, 0),
      paid: studentPayments.filter(p => p.status === 'paid').length,
      unpaid: studentPayments.filter(p => p.status === 'unpaid').length,
    };
  };

  // Event handlers
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleStudentChange = (event: ChangeEvent<{ value: unknown }>) => {
    setSelectedStudent(event.target.value as string);
  };

  // Dialog handlers
  const handleOpenDialog = (payment?: PaymentWithStudent) => {
    setSelectedPayment(payment || null);
    if (payment) {
      setSelectedPaymentStudent({
        id: payment.student.id,
        name: payment.student.name,
        phone: payment.student.phone
      });
    } else {
      setSelectedPaymentStudent(null);
    }
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setSelectedPayment(null);
    setSelectedPaymentStudent(null);
    setIsDialogOpen(false);
  };

  const handleOpenDeleteDialog = (payment: PaymentWithStudent) => {
    setSelectedPayment(payment);
    setIsDeleteDialogOpen(true);
  };

  // Utility functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'success';
      case 'unpaid':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusDisplayText = (status: string) => {
    switch (status) {
      case 'paid':
        return 'Оплатил';
      case 'unpaid':
        return 'Не оплатил';
      default:
        return status;
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'cash':
        return 'success';
      case 'card':
        return 'primary';
      case 'transfer':
        return 'info';
      default:
        return 'default';
    }
  };

  const formatCurrency = (amount: number) => {
    const formattedNumber = amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
    return `${formattedNumber} UZS`;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <PageContainer title="Payments" description="Manage payments">
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={tabValue} onChange={handleTabChange}>
                  <Tab label="All Payments" />
                  <Tab label="Student History" />
                </Tabs>
              </Box>

              <TabPanel value={tabValue} index={0}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                  <Typography variant="h3">Payments</Typography>
                  <Box display="flex" gap={2} alignItems="center">
                    <TextField
                      placeholder="Search payments..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      size="small"
                      sx={{ width: 300 }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <IconFilter size={20} />
                          </InputAdornment>
                        ),
                      }}
                    />
                    <Button variant="contained" color="primary" onClick={() => handleOpenDialog()}>
                      Record New Payment
                    </Button>
                  </Box>
                </Box>

                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Student</TableCell>
                        <TableCell>Amount</TableCell>
                        <TableCell>Date</TableCell>
                        <TableCell>Method</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Description</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {getFilteredPayments().map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <IconReceipt size={16} style={{ marginRight: '8px' }} />
                              <Typography variant="subtitle2" fontWeight={600}>
                                {payment.student.name}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color={payment.status === 'paid' ? 'success.main' : 'inherit'}
                            >
                              {formatCurrency(payment.amount)}
                            </Typography>
                          </TableCell>
                          <TableCell>{new Date(payment.date).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <Chip
                              label={payment.method}
                              color={getMethodColor(payment.method) as any}
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getStatusDisplayText(payment.status)}
                              color={getStatusColor(payment.status) as any}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Box
                              sx={{
                                maxWidth: '300px',
                                maxHeight: '60px',
                                overflowY: 'auto',
                                padding: '4px',
                                '&::-webkit-scrollbar': {
                                  width: '6px',
                                },
                                '&::-webkit-scrollbar-thumb': {
                                  backgroundColor: 'rgba(0,0,0,0.2)',
                                  borderRadius: '3px',
                                },
                              }}
                            >
                              <Typography variant="body2" color="textSecondary">
                                {payment.description}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <IconButton 
                              color="primary" 
                              size="small" 
                              sx={{ mr: 1 }}
                              onClick={() => handleOpenDialog(payment)}
                            >
                              <IconEdit size={18} />
                            </IconButton>
                            <IconButton 
                              color="error" 
                              size="small"
                              onClick={() => handleOpenDeleteDialog(payment)}
                            >
                              <IconTrash size={18} />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                  <Typography variant="h3">Student Payment History</Typography>
                  <FormControl sx={{ minWidth: 200 }}>
                    <InputLabel>Select Student</InputLabel>
                    <Select
                      value={selectedStudent}
                      label="Select Student"
                      onChange={handleStudentChange as any}
                    >
                      <MenuItem value="all">All Students</MenuItem>
                      {students.map((student) => (
                        <MenuItem key={student.id} value={student.id}>
                          {student.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>

                {selectedStudent !== 'all' && (
                  <Grid container spacing={3} sx={{ mb: 3 }}>
                    <Grid item xs={12} md={3}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            Total Payments
                          </Typography>
                          <Typography variant="h4" color="primary">
                            {formatCurrency(getStudentPaymentStats(selectedStudent).total)}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            Paid
                          </Typography>
                          <Typography variant="h4" color="success.main">
                            {getStudentPaymentStats(selectedStudent).paid}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>
                            Unpaid
                          </Typography>
                          <Typography variant="h4" color="error.main">
                            {getStudentPaymentStats(selectedStudent).unpaid}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                )}

                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Student</TableCell>
                        <TableCell>Amount</TableCell>
                        <TableCell>Date</TableCell>
                        <TableCell>Method</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Description</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {getFilteredPayments().map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <IconReceipt size={16} style={{ marginRight: '8px' }} />
                              <Typography variant="subtitle2" fontWeight={600}>
                                {payment.student.name}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography
                              variant="subtitle2"
                              fontWeight={600}
                              color={payment.status === 'paid' ? 'success.main' : 'inherit'}
                            >
                              {formatCurrency(payment.amount)}
                            </Typography>
                          </TableCell>
                          <TableCell>{new Date(payment.date).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <Chip
                              label={payment.method}
                              color={getMethodColor(payment.method) as any}
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getStatusDisplayText(payment.status)}
                              color={getStatusColor(payment.status) as any}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Box
                              sx={{
                                maxWidth: '300px',
                                maxHeight: '60px',
                                overflowY: 'auto',
                                padding: '4px',
                                '&::-webkit-scrollbar': {
                                  width: '6px',
                                },
                                '&::-webkit-scrollbar-thumb': {
                                  backgroundColor: 'rgba(0,0,0,0.2)',
                                  borderRadius: '3px',
                                },
                              }}
                            >
                              <Typography variant="body2" color="textSecondary">
                                {payment.description}
                              </Typography>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </TabPanel>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Payment Dialog */}
      <Dialog open={isDialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedPayment ? 'Edit Payment' : 'Record New Payment'}
        </DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 2 }}>
            <Autocomplete
              options={students}
              getOptionLabel={(option) => `${option.name} (${option.phone})`}
              value={selectedPaymentStudent}
              onChange={(event, newValue) => {
                setSelectedPaymentStudent(newValue ? {
                  id: newValue.id,
                  name: newValue.name,
                  phone: newValue.phone
                } : null);
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Student"
                  fullWidth
                  placeholder="Search by name or phone..."
                />
              )}
              renderOption={(props, option) => (
                <Box component="li" {...props}>
                  <Stack>
                    <Typography variant="subtitle2">{option.name}</Typography>
                    <Typography variant="caption" color="textSecondary">
                      ID: {option.id} • Phone: {option.phone}
                    </Typography>
                  </Stack>
                </Box>
              )}
              isOptionEqualToValue={(option, value) => option.id === value?.id}
              filterOptions={(options, { inputValue }) => {
                const searchTerm = inputValue.toLowerCase();
                return options.filter(
                  option =>
                    option.name.toLowerCase().includes(searchTerm) ||
                    option.phone.toLowerCase().includes(searchTerm) ||
                    option.id.toLowerCase().includes(searchTerm)
                );
              }}
            />
            <TextField
              label="Amount"
              type="number"
              inputRef={amountRef}
              defaultValue={selectedPayment?.amount || ''}
              fullWidth
            />
            <TextField
              label="Date"
              type="date"
              inputRef={dateRef}
              defaultValue={selectedPayment?.date.split('T')[0] || new Date().toISOString().split('T')[0]}
              fullWidth
              InputLabelProps={{ shrink: true }}
            />
            <TextField
              select
              label="Payment Method"
              inputRef={methodRef}
              defaultValue={selectedPayment?.method || 'cash'}
              fullWidth
            >
              <MenuItem value="cash">Cash</MenuItem>
              <MenuItem value="card">Card</MenuItem>
              <MenuItem value="transfer">Transfer</MenuItem>
            </TextField>
            <TextField
              select
              label="Status"
              inputRef={statusRef}
              defaultValue={selectedPayment?.status || 'unpaid'}
              fullWidth
            >
              <MenuItem value="paid">Оплатил</MenuItem>
              <MenuItem value="unpaid">Не оплатил</MenuItem>
            </TextField>
            <TextField
              label="Description"
              multiline
              rows={2}
              inputRef={descriptionRef}
              defaultValue={selectedPayment?.description || ''}
              fullWidth
            />
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button variant="contained" color="primary" onClick={handleSavePayment}>
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onClose={() => setIsDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this payment?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            color="error"
            onClick={() => selectedPayment && handleDeletePayment(selectedPayment.id)}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </PageContainer>
  );
};

export default Payments;