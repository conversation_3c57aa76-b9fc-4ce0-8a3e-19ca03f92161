export interface Teacher {
  id: string;
  name: string;
  email: string;
  phone: string;
  subjects: string[];
  qualifications: string[];
  joinDate: string | Date;
  status: 'active' | 'inactive';
  classes?: TeacherClass[];
}

export interface TeacherWithStats extends Teacher {
  classCount: number;
  totalStudents: number;
  paidStudents: number;
  unpaidStudents: number;
  newStudents: number;
  levelGroups: Record<string, number>;
}

export interface TeacherClass {
  id: string;
  name: string;
  level: 'A1' | 'A2' | 'B1' | 'B2' | 'IELTS' | 'Math' | 'IT' | 'SAT' | 'Individual' | 'Speaking' | 'Kids';
  students: {
    id: string;
    paymentStatus: 'paid' | 'unpaid';
    joinDate: string | Date;
  }[];
}