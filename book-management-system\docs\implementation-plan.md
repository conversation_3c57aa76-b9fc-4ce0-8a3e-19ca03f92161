# Book Sales & Class Materials Management System

## Project Overview

A system to manage and sell educational books integrated with the Class Up system, handling both mandatory class materials and complementary books for students.

### 1. Integration Strategy with Class Up

#### Key Integration Points
- Student registration flow
- Class level assignment and progression
- Payment processing
- User management
- Inventory tracking

#### Business Flow
1. Student registers for a class (Class Up)
2. System identifies required books based on class level
3. Creates combined payment for class fees and required books
4. Processes payment through existing payment system
5. Updates inventory after successful purchase
6. Tracks book sales with class registrations
7. Manages level progressions and new book requirements

### 2. Core Features

#### Book Catalog Management
- Mandatory book sets per level
  * Student's Book
  * Workbook
  * Additional required materials
- Complementary/recommended books
  * Additional practice materials
  * Supplementary reading
  * Level-appropriate resources
- Book metadata
  * ISBN
  * Title, Author, Publisher
  * Level designation
  * Price
  * Cover image
  * Stock quantity

#### Class-Book Association & Level Progression
- Define required book sets per level
- Set pricing bundles
- Track book requirements per class
- Manage complementary book recommendations
- Level Progression Management:
  * Track class level transitions (e.g., A1 → A2)
  * Automatic notification system for new book requirements
  * Batch processing for class-wide book requirements
  * Progress tracking for book purchases during transition
  * Historical record of level progressions

#### Sales & Payment Integration
- Combined payment processing for:
  * Class registration fees
  * Required book sets
  * Additional materials
- Integration with existing payment system
- Receipt generation
- Sales history tracking

#### Inventory Management
- Stock level tracking
- Low stock alerts
- Purchase order management
- Stock receipt processing
- Historical sales data

#### Student Portal
- View required books for registered class
- Browse complementary materials
- Purchase history
- Digital receipt access
- Level progression notifications

#### Admin Features
- Book inventory management
- Sales reports
- Stock level monitoring
- Bundle configuration
- Pricing management
- Level progression management

### 3. Technical Stack (Matching Class Up)

```
Frontend:
- Next.js 14 with App Router
- TypeScript
- Material UI v5
- React Query
- JWT authentication

Backend:
- Next.js API Routes
- Prisma ORM
- PostgreSQL
- Shared utilities with Class Up
```

### 4. Database Schema

```prisma
model Class {
  id                String    @id @default(cuid())
  name              String
  level             String
  stage             Stage
  language          Language
  bookSet           BookSet?  @relation(fields: [bookSetId], references: [id])
  bookSetId         String?
  levelProgressions LevelProgression[]
  bookSales         BookSale[]
  students          Student[]
}

model Book {
  id            String    @id @default(cuid())
  isbn          String    @unique
  title         String
  author        String
  publisher     String
  publishYear   Int
  level         String
  type          BookType  @default(TEXTBOOK)
  price         Float
  description   String?
  coverImage    String?
  stockQuantity Int       @default(0)
  minimumStock  Int       @default(10)
  bookSets      BookSet[]
  sales         BookSale[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

enum BookType {
  TEXTBOOK
  WORKBOOK
  SUPPLEMENTARY
  RECOMMENDED
}

model BookSet {
  id                String    @id @default(cuid())
  name              String
  level             String
  books             Book[]
  totalPrice        Float
  classes           Class[]
  sales             BookSale[]
  levelProgressions LevelProgression[]
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}

model LevelProgression {
  id                String    @id @default(cuid())
  class             Class     @relation(fields: [classId], references: [id])
  classId           String
  fromLevel         String
  toLevel           String
  progressionDate   DateTime
  bookSet           BookSet   @relation(fields: [newBookSetId], references: [id])
  newBookSetId      String
  students          Student[]
  status            ProgressionStatus @default(PENDING)
  completedStudents Student[] @relation("CompletedBookPurchases")
  bookSales         BookSale[]
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}

enum ProgressionStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

model BookSale {
  id                String    @id @default(cuid())
  student           Student   @relation(fields: [studentId], references: [id])
  studentId         String
  class             Class     @relation(fields: [classId], references: [id])
  classId           String
  books             Book[]
  bookSet           BookSet?  @relation(fields: [bookSetId], references: [id])
  bookSetId         String?
  payment           Payment   @relation(fields: [paymentId], references: [id])
  paymentId         String
  levelProgression  LevelProgression? @relation(fields: [progressionId], references: [id])
  progressionId     String?
  totalAmount       Float
  saleDate          DateTime  @default(now())
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}

model Payment {
  id            String    @id @default(cuid())
  student       Student   @relation(fields: [studentId], references: [id])
  studentId     String
  amount        Float
  type          PaymentType
  status        String
  bookSale      BookSale?
}

enum PaymentType {
  CLASS_FEE
  BOOK_PURCHASE
  COMBINED
}
```

### 5. API Structure

#### Books & Sets
- GET /api/books
- GET /api/books/:id
- POST /api/books
- PUT /api/books/:id
- GET /api/book-sets
- GET /api/book-sets/:level
- POST /api/book-sets
- PUT /api/book-sets/:id

#### Sales
- POST /api/sales/calculate-total
- POST /api/sales/process
- GET /api/sales/history
- GET /api/sales/student/:id

#### Level Progression
- POST /api/classes/:id/progress-level
- GET /api/classes/:id/progression-status
- GET /api/students/:id/pending-books
- POST /api/progression/:id/complete-purchase
- GET /api/progression/:id/remaining-students

#### Inventory
- GET /api/inventory/status
- POST /api/inventory/receive
- GET /api/inventory/alerts

### 6. Mock Data Structure

```typescript
export const mockBooks = [
  {
    id: "1",
    isbn: "978-0134685991",
    title: "Pioneer A1 Student's Book",
    author: "Mitchell, H.Q.",
    publisher: "MM Publications",
    publishYear: 2023,
    level: "A1",
    type: "TEXTBOOK",
    price: 45.99,
    stockQuantity: 50,
    minimumStock: 10
  },
  {
    id: "2",
    isbn: "978-0134685992",
    title: "Pioneer A1 Workbook",
    author: "Mitchell, H.Q.",
    publisher: "MM Publications",
    publishYear: 2023,
    level: "A1",
    type: "WORKBOOK",
    price: 25.99,
    stockQuantity: 50,
    minimumStock: 10
  }
];

export const mockBookSets = [
  {
    id: "1",
    name: "A1 Level Required Materials",
    level: "A1",
    books: ["1", "2"],
    totalPrice: 71.98
  }
];

export const mockLevelProgressions = [
  {
    id: "1",
    classId: "class1",
    fromLevel: "A1",
    toLevel: "A2",
    progressionDate: "2024-03-01T00:00:00Z",
    newBookSetId: "2",
    status: "PENDING",
    completedStudents: []
  }
];

export const mockSales = [
  {
    id: "1",
    studentId: "student1",
    classId: "class1",
    bookSetId: "1",
    paymentId: "payment1",
    totalAmount: 71.98,
    saleDate: "2024-02-24T09:00:00Z"
  }
];
```

### 7. Development Phases

#### Phase 1: Foundation (2 weeks)
- Project setup
- Database schema implementation
- Basic book management
- Mock data implementation

#### Phase 2: Core Features (2 weeks)
- Book set management
- Inventory tracking
- Basic sales processing
- Class integration
- Level progression system:
  * Class level transition management
  * Book requirement notifications
  * Purchase tracking during transitions

#### Phase 3: Payment Integration (2 weeks)
- Combined payment processing
- Receipt generation
- Sales history
- Student portal features
- Level progression notifications

#### Phase 4: Class Up Integration (1 week)
- Registration flow integration
- User system connection
- Combined payments
- UI/UX harmonization

#### Phase 5: Testing & Launch (1 week)
- Integration testing
- Performance optimization
- Documentation
- Staff training

### 8. Level Progression Workflow

1. Class Level Transition
   - Teacher/Admin initiates level progression
   - System creates LevelProgression record
   - Identifies all students in the class
   - Sets up new book requirements

2. Notification System
   - Automated notifications to all class students
   - Details of required new books
   - Payment instructions
   - Deadline for purchases

3. Purchase Tracking
   - Monitor student book purchases
   - Track completion status
   - Follow-up notifications for pending purchases
   - Progress dashboard for administrators

4. Completion Management
   - Mark students as complete upon purchase
   - Track overall class progression status
   - Generate reports on purchase completion
   - Handle exceptions and special cases

### 9. User Interface Requirements

#### Admin/Teacher Dashboard
- Level progression initiation
- Progress tracking view
- Student purchase status
- Reminder system management

#### Student Portal
- Level progression notifications
- Required book list
- Purchase interface
- Current status tracking

### Next Steps

1. Review current Class Up registration and payment flow
2. Set up development environment
3. Create initial project structure
4. Implement level progression system
5. Begin Phase 1 implementation with mock data
6. Regular alignment with Class Up team