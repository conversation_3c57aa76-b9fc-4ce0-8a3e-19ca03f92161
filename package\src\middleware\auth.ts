import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, hasPermission, User } from '@/lib/auth';
import { auditLog } from '@/lib/audit';

export interface AuthenticatedRequest extends NextRequest {
  user?: User;
}

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Rate limiting configuration
const RATE_LIMIT_WINDOW_MS = parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'); // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100');

// Rate limiting middleware
export function rateLimit(request: NextRequest): NextResponse | null {
  const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  const now = Date.now();
  
  // Clean up expired entries
  for (const [ip, data] of rateLimitStore.entries()) {
    if (now > data.resetTime) {
      rateLimitStore.delete(ip);
    }
  }
  
  // Get or create rate limit data for this IP
  let rateLimitData = rateLimitStore.get(clientIP);
  
  if (!rateLimitData || now > rateLimitData.resetTime) {
    rateLimitData = {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW_MS
    };
    rateLimitStore.set(clientIP, rateLimitData);
    return null; // Allow request
  }
  
  rateLimitData.count++;
  
  if (rateLimitData.count > RATE_LIMIT_MAX_REQUESTS) {
    // Log rate limit violation
    auditLog({
      action: 'RATE_LIMIT_EXCEEDED',
      entity: 'API',
      entityId: 'rate-limit',
      ipAddress: clientIP,
      userAgent: request.headers.get('user-agent') || 'unknown',
      details: { requestCount: rateLimitData.count }
    });
    
    return NextResponse.json(
      { 
        error: 'Rate limit exceeded',
        message: 'Too many requests. Please try again later.',
        retryAfter: Math.ceil((rateLimitData.resetTime - now) / 1000)
      },
      { 
        status: 429,
        headers: {
          'Retry-After': Math.ceil((rateLimitData.resetTime - now) / 1000).toString(),
          'X-RateLimit-Limit': RATE_LIMIT_MAX_REQUESTS.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': rateLimitData.resetTime.toString()
        }
      }
    );
  }
  
  return null; // Allow request
}

// Authentication middleware
export function withAuth(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>,
  options: {
    requiredPermissions?: string[];
    allowedRoles?: string[];
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      // Apply rate limiting
      const rateLimitResponse = rateLimit(request);
      if (rateLimitResponse) {
        return rateLimitResponse;
      }
      
      // Verify authentication
      const user = requireAuth(request);
      
      if (!user) {
        auditLog({
          action: 'UNAUTHORIZED_ACCESS_ATTEMPT',
          entity: 'API',
          entityId: request.nextUrl.pathname,
          ipAddress: request.ip || request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          details: { path: request.nextUrl.pathname, method: request.method }
        });
        
        return NextResponse.json(
          { error: 'Unauthorized', message: 'Authentication required' },
          { status: 401 }
        );
      }
      
      // Check required permissions
      if (options.requiredPermissions) {
        const hasRequiredPermissions = options.requiredPermissions.every(permission =>
          hasPermission(user, permission)
        );
        
        if (!hasRequiredPermissions) {
          auditLog({
            userId: user.id,
            action: 'INSUFFICIENT_PERMISSIONS',
            entity: 'API',
            entityId: request.nextUrl.pathname,
            ipAddress: request.ip || request.headers.get('x-forwarded-for') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
            details: { 
              requiredPermissions: options.requiredPermissions,
              userPermissions: user.permissions,
              path: request.nextUrl.pathname,
              method: request.method
            }
          });
          
          return NextResponse.json(
            { error: 'Forbidden', message: 'Insufficient permissions' },
            { status: 403 }
          );
        }
      }
      
      // Check allowed roles
      if (options.allowedRoles && !options.allowedRoles.includes(user.role)) {
        auditLog({
          userId: user.id,
          action: 'ROLE_ACCESS_DENIED',
          entity: 'API',
          entityId: request.nextUrl.pathname,
          ipAddress: request.ip || request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          details: { 
            allowedRoles: options.allowedRoles,
            userRole: user.role,
            path: request.nextUrl.pathname,
            method: request.method
          }
        });
        
        return NextResponse.json(
          { error: 'Forbidden', message: 'Role not authorized for this resource' },
          { status: 403 }
        );
      }
      
      // Add user to request
      const authenticatedRequest = request as AuthenticatedRequest;
      authenticatedRequest.user = user;
      
      // Log successful API access
      auditLog({
        userId: user.id,
        action: 'API_ACCESS',
        entity: 'API',
        entityId: request.nextUrl.pathname,
        ipAddress: request.ip || request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        details: { path: request.nextUrl.pathname, method: request.method }
      });
      
      return await handler(authenticatedRequest);
      
    } catch (error) {
      console.error('Auth middleware error:', error);
      
      auditLog({
        action: 'AUTH_MIDDLEWARE_ERROR',
        entity: 'API',
        entityId: request.nextUrl.pathname,
        ipAddress: request.ip || request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
      
      return NextResponse.json(
        { error: 'Internal Server Error', message: 'Authentication failed' },
        { status: 500 }
      );
    }
  };
}

// Input validation middleware
export function validateInput<T>(schema: any) {
  return (handler: (request: AuthenticatedRequest, validatedData: T) => Promise<NextResponse>) => {
    return async (request: AuthenticatedRequest): Promise<NextResponse> => {
      try {
        let data: any;
        
        if (request.method === 'GET') {
          // Validate query parameters
          const searchParams = Object.fromEntries(request.nextUrl.searchParams.entries());
          data = schema.parse(searchParams);
        } else {
          // Validate request body
          const body = await request.json();
          data = schema.parse(body);
        }
        
        return await handler(request, data);
        
      } catch (error) {
        auditLog({
          userId: request.user?.id,
          action: 'VALIDATION_ERROR',
          entity: 'API',
          entityId: request.nextUrl.pathname,
          ipAddress: request.ip || request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          details: { error: error instanceof Error ? error.message : 'Validation failed' }
        });
        
        return NextResponse.json(
          { 
            error: 'Validation Error',
            message: 'Invalid input data',
            details: error instanceof Error ? error.message : 'Validation failed'
          },
          { status: 400 }
        );
      }
    };
  };
}
