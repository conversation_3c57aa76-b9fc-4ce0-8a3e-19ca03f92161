import { Prisma } from '@prisma/client'

export type StudentWithClasses = Prisma.StudentGetPayload<{
  include: { classes: true }
}>

export type ClassWithRelations = Prisma.ClassGetPayload<{
  include: { teacher: true; cabinet: true; students: true }
}> & {
  stage: 'EARLY' | 'MIDDLE' | 'LATE';
  language: 'RUSSIAN' | 'UZBEK' | 'MIXED';
}

export type TeacherWithClasses = Prisma.TeacherGetPayload<{
  include: { classes: true }
}>

export type CabinetWithClasses = Prisma.CabinetGetPayload<{
  include: { classes: true }
}>

export type PaymentWithStudent = Prisma.PaymentGetPayload<{
  include: { student: true }
}>