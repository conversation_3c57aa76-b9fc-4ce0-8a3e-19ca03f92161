import { useState, useEffect, useCallback } from 'react';
import { PaymentWithStudent } from '@/types/payment';

// Event bus for payment updates
const paymentEventBus = {
  listeners: new Set<() => void>(),
  subscribe(listener: () => void) {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  },
  publish() {
    this.listeners.forEach(listener => listener());
  },
};

export const usePayments = () => {
  const [payments, setPayments] = useState<PaymentWithStudent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPayments = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/payments');
      if (!response.ok) throw new Error('Failed to fetch payments');
      const data = await response.json();
      setPayments(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPayments();
    // Subscribe to payment updates
    const unsubscribe = paymentEventBus.subscribe(fetchPayments);
    return unsubscribe;
  }, [fetchPayments]);

  const updatePayment = useCallback(async (paymentData: Partial<PaymentWithStudent> & { id: string }) => {
    try {
      const response = await fetch(`/api/payments?id=${paymentData.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(paymentData),
      });

      if (!response.ok) throw new Error('Failed to update payment');
      const updatedPayment = await response.json();

      setPayments(prevPayments =>
        prevPayments.map(payment =>
          payment.id === updatedPayment.id ? updatedPayment : payment
        )
      );

      // Notify all subscribers about the update
      paymentEventBus.publish();
      return updatedPayment;
    } catch (error) {
      throw error;
    }
  }, []);

  const createPayment = useCallback(async (paymentData: Omit<PaymentWithStudent, 'id' | 'student'> & { studentId: string }) => {
    try {
      const response = await fetch('/api/payments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(paymentData),
      });

      if (!response.ok) throw new Error('Failed to create payment');
      const newPayment = await response.json();

      setPayments(prevPayments => [newPayment, ...prevPayments]);

      // Notify all subscribers about the update
      paymentEventBus.publish();
      return newPayment;
    } catch (error) {
      throw error;
    }
  }, []);

  const deletePayment = useCallback(async (paymentId: string) => {
    try {
      const response = await fetch(`/api/payments?id=${paymentId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete payment');

      setPayments(prevPayments =>
        prevPayments.filter(payment => payment.id !== paymentId)
      );

      // Notify all subscribers about the update
      paymentEventBus.publish();
    } catch (error) {
      throw error;
    }
  }, []);

  const getStudentPaymentStatus = useCallback((studentId: string) => {
    const studentPayments = payments.filter(p => p.studentId === studentId);
    if (studentPayments.length === 0) return 'unpaid';

    const hasUnpaidPayments = studentPayments.some(p => p.status === 'unpaid');
    return hasUnpaidPayments ? 'unpaid' : 'paid';
  }, [payments]);

  return {
    payments,
    loading,
    error,
    updatePayment,
    createPayment,
    deletePayment,
    getStudentPaymentStatus,
    refreshPayments: fetchPayments,
  };
};