'use client';
import { useState, useMemo, useCallback, useEffect } from 'react';
import { usePayments } from '@/hooks/usePayments';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Stack,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Chip,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Paper,
  Collapse,
  Tooltip,
  useTheme,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  TextField,
  InputAdornment,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import PageContainer from '@/app/(DashboardLayout)/components/container/PageContainer';
import { ClassWithRelations } from '@/types';
import AddIcon from '@mui/icons-material/Add';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import EditIcon from '@mui/icons-material/Edit';
import ClassDialog from '../classes/ClassDialog';
import StudentDialog from '../students/StudentDialog';

interface DialogState {
  open: boolean;
  mode: 'create' | 'edit';
  classData?: ClassWithRelations;
}

interface StudentDialogState {
  open: boolean;
  classId: string | null;
}

interface Student {
  id: string;
  name: string;
  phone: string;
  paymentStatus: string;
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('uz-UZ', {
    style: 'currency',
    currency: 'UZS',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const Timetable = () => {
  const theme = useTheme();
  const { getStudentPaymentStatus } = usePayments();
  const [classes, setClasses] = useState<ClassWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogState, setDialogState] = useState<DialogState>({
    open: false,
    mode: 'create'
  });
  const [studentDialogState, setStudentDialogState] = useState<StudentDialogState>({
    open: false,
    classId: null
  });
  const [availableStudents, setAvailableStudents] = useState<Student[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [newStudentDialogOpen, setNewStudentDialogOpen] = useState(false);
  
  const filteredStudents = useMemo(() => {
    if (!searchQuery.trim()) return availableStudents;
    
    const query = searchQuery.toLowerCase();
    return availableStudents.filter(student =>
      student.name.toLowerCase().includes(query) ||
      student.phone.toLowerCase().includes(query)
    );
  }, [availableStudents, searchQuery]);
  
  const teachers = useMemo(() =>
    Array.from(new Set(classes.map(c => c.teacher.name))),
    [classes]
  );
  const [selectedTeacher, setSelectedTeacher] = useState<string>('');
  const [selectedLanguage, setSelectedLanguage] = useState<string>('ALL');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedClasses, setExpandedClasses] = useState<string[]>([]);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    open: boolean;
    classId: string | null;
    studentId: string | null;
  }>({
    open: false,
    classId: null,
    studentId: null
  });

  const [highlightedClassId, setHighlightedClassId] = useState<string | null>(null);

  const languages = ['ALL', 'RUSSIAN', 'UZBEK', 'MIXED'];

  const handleDeleteConfirmation = (classId: string, studentId: string) => {
    setDeleteConfirmation({
      open: true,
      classId,
      studentId
    });
  };

  const handleConfirmDelete = async () => {
    if (deleteConfirmation.classId && deleteConfirmation.studentId) {
      await handleDeleteStudent(deleteConfirmation.classId, deleteConfirmation.studentId);
    }
    setDeleteConfirmation({
      open: false,
      classId: null,
      studentId: null
    });
  };

  const handleCancelDelete = () => {
    setDeleteConfirmation({
      open: false,
      classId: null,
      studentId: null
    });
  };

  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  
  const quickFilters = [
    {
      label: 'Available Classes',
      value: 'available',
      filter: (c: ClassWithRelations) => c.students.length < c.cabinet.capacity
    },
    {
      label: 'Morning Classes',
      value: 'morning',
      filter: (c: ClassWithRelations) => {
        const schedule = c.schedule as { startTime: string }[];
        return schedule.some(s => s.startTime < '12:00');
      }
    },
    {
      label: 'Afternoon Classes',
      value: 'afternoon',
      filter: (c: ClassWithRelations) => {
        const schedule = c.schedule as { startTime: string }[];
        return schedule.some(s => s.startTime >= '14:00' && s.startTime < '17:00');
      }
    },
    {
      label: 'Evening Classes',
      value: 'evening',
      filter: (c: ClassWithRelations) => {
        const schedule = c.schedule as { startTime: string }[];
        return schedule.some(s => s.startTime >= '16:00');
      }
    },
  ];

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/classes');
      if (!response.ok) throw new Error('Failed to fetch classes');
      const data = await response.json();
      setClasses(data);
      if (data.length > 0) {
        setSelectedTeacher(data[0].teacher.name);
      }
    } catch (error) {
      console.error('Failed to fetch classes:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClasses();
  }, []);

  const handleOpenCreate = () => {
    setDialogState({
      open: true,
      mode: 'create'
    });
  };

  const handleOpenEdit = (classData: ClassWithRelations) => {
    setDialogState({
      open: true,
      mode: 'edit',
      classData
    });
  };

  const handleClose = () => {
    setDialogState({
      open: false,
      mode: 'create'
    });
  };

  const handleSave = async (classData: Partial<ClassWithRelations>) => {
    try {
      const method = dialogState.mode === 'create' ? 'POST' : 'PUT';
      const url = method === 'PUT' ? `/api/classes?id=${classData.id}` : '/api/classes';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(classData),
      });

      if (!response.ok) throw new Error(`Failed to ${dialogState.mode} class`);
      
      const savedClass = await response.json();
      
      if (dialogState.mode === 'create') {
        setClasses(prev => [...prev, savedClass]);
      } else {
        setClasses(prev =>
          prev.map(c => c.id === savedClass.id ? savedClass : c)
        );
      }
      handleClose();
    } catch (error) {
      console.error('Failed to save class:', error);
    }
  };

  const handleDelete = useCallback(async () => {
    if (dialogState.classData) {
      try {
        const response = await fetch(`/api/classes?id=${dialogState.classData.id}`, {
          method: 'DELETE',
        });

        if (!response.ok) throw new Error('Failed to delete class');

        setClasses(prev => prev.filter(c => c.id !== dialogState.classData?.id));
        handleClose();
      } catch (error) {
        console.error('Failed to delete class:', error);
      }
    }
  }, [dialogState.classData]);

  const handleOpenAddStudents = async (classId: string) => {
    setIsSearching(true);
    setSearchQuery('');
    try {
      const response = await fetch('/api/students');
      if (!response.ok) throw new Error('Failed to fetch students');
      const allStudents = await response.json();
      
      const currentClass = classes.find(c => c.id === classId);
      if (currentClass) {
        const currentStudentIds = currentClass.students.map(s => s.id);
        const availableStudents = allStudents.filter(
          (student: Student) => !currentStudentIds.includes(student.id)
        );
        
        setAvailableStudents(availableStudents);
        setStudentDialogState({
          open: true,
          classId
        });
      }
    } catch (error) {
      console.error('Failed to fetch available students:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleCloseAddStudents = () => {
    setStudentDialogState({
      open: false,
      classId: null
    });
    setSearchQuery('');
    setIsSearching(false);
  };

  const handleOpenNewStudentDialog = () => {
    setNewStudentDialogOpen(true);
  };

  const handleCloseNewStudentDialog = () => {
    setNewStudentDialogOpen(false);
  };

  const handleSaveNewStudent = async (formData: any) => {
    try {
      const response = await fetch('/api/students', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to create student');
      
      const savedStudent = await response.json();
      
      // Add the new student to the available students list
      setAvailableStudents(prev => [...prev, savedStudent]);
      
      // Close the dialog
      setNewStudentDialogOpen(false);
    } catch (error) {
      console.error('Failed to save student:', error);
    }
  };

  const handleDeleteStudent = async (classId: string, studentId: string) => {
    try {
      const currentClass = classes.find(c => c.id === classId);
      if (!currentClass) return;

      const updatedStudentIds = currentClass.students
        .filter(s => s.id !== studentId)
        .map(s => s.id);

      const response = await fetch(`/api/classes?id=${classId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...currentClass,
          students: updatedStudentIds,
        }),
      });

      if (!response.ok) throw new Error('Failed to update class');

      const updatedClass = await response.json();
      setClasses(prev =>
        prev.map(c => c.id === classId ? updatedClass : c)
      );
    } catch (error) {
      console.error('Failed to remove student:', error);
    }
  };

  const handleAddStudent = async (studentId: string) => {
    if (studentDialogState.classId) {
      try {
        const currentClass = classes.find(c => c.id === studentDialogState.classId);
        if (!currentClass) return;

        if (currentClass.students.length >= currentClass.cabinet.capacity) {
          alert('Cannot add more students. Cabinet capacity reached.');
          return;
        }

        const updatedStudentIds = [...currentClass.students.map(s => s.id), studentId];

        const response = await fetch(`/api/classes?id=${studentDialogState.classId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            ...currentClass,
            students: updatedStudentIds,
          }),
        });

        if (!response.ok) throw new Error('Failed to update class');

        const updatedClass = await response.json();
        setClasses(prev =>
          prev.map(c => c.id === studentDialogState.classId ? updatedClass : c)
        );

        setAvailableStudents(prev => prev.filter(s => s.id !== studentId));
      } catch (error) {
        console.error('Failed to add student:', error);
      }
    }
  };

  const filteredClasses = useMemo(() =>
    classes.filter(c => {
      // Teacher filter
      if (c.teacher.name !== selectedTeacher) return false;
      
      // Language filter
      if (selectedLanguage !== 'ALL' && c.language !== selectedLanguage) return false;
      
      // Quick filter
      if (activeFilter) {
        const filter = quickFilters.find(f => f.value === activeFilter);
        if (filter && !filter.filter(c)) return false;
      }
      
      // Search term
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesClass = c.name.toLowerCase().includes(searchLower);
        const matchesTeacher = c.teacher.name.toLowerCase().includes(searchLower);
        if (!matchesClass && !matchesTeacher) return false;
      }
      
      return true;
    }),
    [selectedTeacher, selectedLanguage, searchTerm, classes, activeFilter]
  );

  const sortClassesByTime = useCallback((classes: ClassWithRelations[]) => {
    return [...classes].sort((a, b) => {
      const scheduleA = a.schedule as { day: string; startTime: string; endTime: string; }[];
      const scheduleB = b.schedule as { day: string; startTime: string; endTime: string; }[];
      
      const timeA = scheduleA[0]?.startTime || '00:00';
      const timeB = scheduleB[0]?.startTime || '00:00';
      
      return timeA.localeCompare(timeB);
    });
  }, []);

  const mwfClasses = useMemo(() => {
    const filtered = filteredClasses.filter(c => {
      const schedule = c.schedule as { day: string; startTime: string; endTime: string; }[];
      return schedule.some(s => ['Monday', 'Wednesday', 'Friday'].includes(s.day));
    });
    return sortClassesByTime(filtered);
  }, [filteredClasses, sortClassesByTime]);

  const ttsClasses = useMemo(() => {
    const filtered = filteredClasses.filter(c => {
      const schedule = c.schedule as { day: string; startTime: string; endTime: string; }[];
      return schedule.some(s => ['Tuesday', 'Thursday', 'Saturday'].includes(s.day));
    });
    return sortClassesByTime(filtered);
  }, [filteredClasses, sortClassesByTime]);

  const toggleClassExpansion = (classId: string) => {
    setExpandedClasses(prev => 
      prev.includes(classId) 
        ? prev.filter(id => id !== classId)
        : [...prev, classId]
    );
  };

  const getPaymentStatusColor = (status: string) => {
    return status === 'paid' ? 'success' : 'error';
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'EARLY': return 'info';
      case 'MIDDLE': return 'warning';
      case 'LATE': return 'success';
      default: return 'default';
    }
  };

  const getLanguageColor = (language: string) => {
    switch (language) {
      case 'RUSSIAN': return 'primary';
      case 'UZBEK': return 'secondary';
      case 'MIXED': return 'warning';
      default: return 'default';
    }
  };

  const getTimeUntilStart = (openingDate?: Date | null) => {
    if (!openingDate) return null;
    
    const start = new Date(openingDate);
    const now = new Date();
    const diffDays = Math.ceil((start.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  const getStartDateColor = (daysUntilStart: number | null) => {
    if (daysUntilStart === null) return 'default';
    if (daysUntilStart <= 0) return 'success';
    if (daysUntilStart <= 3) return 'warning';
    if (daysUntilStart <= 7) return 'info';
    return 'default';
  };

  const getCabinetFillColor = (studentsCount: number, capacity: number) => {
    const remainingSeats = capacity - studentsCount;
    if (remainingSeats <= 0) return 'error';
    if (remainingSeats <= 2) return 'error';
    if (remainingSeats <= 5) return 'warning';
    return 'success';
  };

  const renderClassCard = (classInfo: ClassWithRelations) => {
    const isExpanded = expandedClasses.includes(classInfo.id);
    const schedule = classInfo.schedule as { day: string; startTime: string; endTime: string; }[];
    const scheduleStr = schedule.length > 0
      ? `${schedule[0].startTime} - ${schedule[0].endTime}`
      : 'No time set';

    const daysUntilStart = getTimeUntilStart(classInfo.openingDate);
    const startDateColor = getStartDateColor(daysUntilStart);
    const cabinetFillColor = getCabinetFillColor(classInfo.students.length, classInfo.cabinet.capacity);

    const startDateText = daysUntilStart !== null
      ? daysUntilStart <= 0
        ? 'Started'
        : `Starts in ${daysUntilStart} days`
      : 'Start date not set';

    return (
      <Card key={classInfo.id} sx={{ mb: 2, minHeight: '150px' }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography
                variant="h5"
                component="div"
                sx={{ fontWeight: 500, mb: 1 }}
              >
                {classInfo.name}
              </Typography>
              <Typography
                variant="subtitle1"
                sx={{
                  color: 'text.secondary',
                  mb: 2,
                  '& .separator': {
                    mx: 1,
                    color: 'text.disabled'
                  }
                }}
              >
                Level {classInfo.level} <span className="separator">|</span> {formatCurrency(classInfo.courseAmount)}
              </Typography>
              <Typography color="textSecondary" sx={{ fontSize: '1rem', mb: 0.5 }}>
                Teacher: {classInfo.teacher.name}
              </Typography>
              <Typography color="textSecondary" sx={{ fontSize: '1rem', mb: 0.5 }}>
                {scheduleStr}
              </Typography>
              <Typography color="textSecondary" sx={{ fontSize: '1rem' }}>
                Start date: {classInfo.openingDate
                  ? new Date(classInfo.openingDate).toLocaleDateString('en-GB', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric'
                    })
                  : 'Not set'
                }
              </Typography>
            </Box>
            <Stack direction="row" spacing={1}>
              <IconButton size="small" onClick={() => toggleClassExpansion(classInfo.id)}>
                {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
              <IconButton size="small" onClick={() => handleOpenEdit(classInfo)}>
                <EditIcon />
              </IconButton>
            </Stack>
          </Box>

          <Stack direction="row" spacing={1} mt={1}>
            <Chip
              label={classInfo.cabinet.name}
              size="small"
              color="primary"
              sx={{ fontSize: '1rem' }}
            />
            <Chip
              label={`${classInfo.students.length}/${classInfo.cabinet.capacity} students`}
              size="small"
              color={cabinetFillColor}
              sx={{ fontSize: '1rem' }}
            />
            <Chip
              label={startDateText}
              size="small"
              color={startDateColor}
              sx={{ fontSize: '1rem' }}
            />
            <Chip
              label={classInfo.language}
              size="small"
              color={getLanguageColor(classInfo.language)}
              sx={{ fontSize: '1rem' }}
            />
          </Stack>

          <Collapse in={isExpanded}>
            <Box mt={2}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Students
                </Typography>
                <Button
                  startIcon={<AddIcon />}
                  onClick={() => handleOpenAddStudents(classInfo.id)}
                  variant="contained"
                  size="small"
                  color="primary"
                  sx={{
                    borderRadius: '20px',
                    boxShadow: 'none',
                    '&:hover': {
                      boxShadow: 'none',
                      backgroundColor: theme.palette.primary.dark,
                    }
                  }}
                >
                  Add Student
                </Button>
              </Box>
              <Grid container spacing={2}>
                {classInfo.students.map((student) => (
                  <Grid item xs={12} key={student.id}>
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                        py: 1
                      }}
                    >
                      <Box sx={{ display: 'flex', gap: 2, fontSize: '1.1rem' }}>
                        <Typography>{student.name}</Typography>
                        <Typography color="textSecondary">{student.phone}</Typography>
                      </Box>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Chip
                          label={getStudentPaymentStatus(student.id)}
                          size="small"
                          sx={{ fontSize: '0.9rem' }}
                          color={
                            getPaymentStatusColor(getStudentPaymentStatus(student.id))
                          }
                        />
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteConfirmation(classInfo.id, student.id)}
                        >
                          <DeleteOutlineIcon />
                        </IconButton>
                      </Stack>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Collapse>
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <PageContainer title="Timetable" description="Class schedule by teacher">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </PageContainer>
    );
  }

  return (
    <PageContainer title="Timetable" description="Class schedule by teacher">
      <Box sx={{ mb: 3 }}>
        <Typography variant="h3" gutterBottom>Class Schedule</Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Select Teacher</InputLabel>
              <Select
                value={selectedTeacher}
                label="Select Teacher"
                onChange={(e) => setSelectedTeacher(e.target.value)}
              >
                {teachers.map((teacher) => (
                  <MenuItem key={teacher} value={teacher}>
                    {teacher}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Language</InputLabel>
              <Select
                value={selectedLanguage}
                label="Language"
                onChange={(e) => setSelectedLanguage(e.target.value)}
              >
                {languages.map((lang) => (
                  <MenuItem key={lang} value={lang}>
                    {lang}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {quickFilters.map((filter) => (
            <Button
              key={filter.value}
              variant={activeFilter === filter.value ? 'contained' : 'outlined'}
              size="small"
              onClick={() => setActiveFilter(activeFilter === filter.value ? null : filter.value)}
              sx={{ borderRadius: '20px' }}
            >
              {filter.label}
            </Button>
          ))}
        </Box>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, mb: { xs: 2, md: 0 } }}>
            <Typography variant="h5" gutterBottom color="primary">
              Monday/Wednesday/Friday
            </Typography>
            {mwfClasses.map(renderClassCard)}
            {mwfClasses.length === 0 && (
              <Typography color="textSecondary" align="center" sx={{ py: 4 }}>
                No classes scheduled
              </Typography>
            )}
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h5" gutterBottom color="primary">
              Tuesday/Thursday/Saturday
            </Typography>
            {ttsClasses.map(renderClassCard)}
            {ttsClasses.length === 0 && (
              <Typography color="textSecondary" align="center" sx={{ py: 4 }}>
                No classes scheduled
              </Typography>
            )}
          </Paper>
        </Grid>
      </Grid>

      <ClassDialog
        open={dialogState.open}
        onClose={handleClose}
        onSave={handleSave}
        onDelete={dialogState.mode === 'edit' ? handleDelete : undefined}
        classData={dialogState.classData}
        mode={dialogState.mode}
      />

      <Dialog
        open={studentDialogState.open}
        onClose={handleCloseAddStudents}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Add Student to Class</Typography>
            <Button
              startIcon={<AddIcon />}
              onClick={handleOpenNewStudentDialog}
              variant="contained"
              size="small"
              color="primary"
              sx={{
                borderRadius: '20px',
                boxShadow: 'none',
                '&:hover': {
                  boxShadow: 'none',
                  backgroundColor: theme.palette.primary.dark,
                }
              }}
            >
              Add New Student
            </Button>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              label="Search students"
              variant="outlined"
              value={searchQuery}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
              placeholder="Search by name or phone number"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
            />
          </Box>
          <List>
            {isSearching ? (
              <ListItem>
                <Box display="flex" justifyContent="center" width="100%" p={2}>
                  <CircularProgress size={24} />
                </Box>
              </ListItem>
            ) : filteredStudents.length === 0 ? (
              <ListItem>
                <ListItemText
                  primary={searchQuery.trim() ? "No matching students found" : "No available students to add"}
                />
              </ListItem>
            ) : (
              filteredStudents.map((student) => (
                <ListItem
                  key={student.id}
                  secondaryAction={
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => handleAddStudent(student.id)}
                    >
                      Add
                    </Button>
                  }
                >
                  <ListItemText
                    primary={student.name}
                    secondary={`${student.phone} - ${student.paymentStatus}`}
                  />
                </ListItem>
              ))
            )}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAddStudents}>Close</Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={deleteConfirmation.open}
        onClose={handleCancelDelete}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Remove Student</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to remove this student from the class?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete}>Cancel</Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained">
            Remove
          </Button>
        </DialogActions>
      </Dialog>

      <StudentDialog
        open={newStudentDialogOpen}
        onClose={handleCloseNewStudentDialog}
        onSave={handleSaveNewStudent}
        student={undefined}
      />
    </PageContainer>
  );
};

export default Timetable;