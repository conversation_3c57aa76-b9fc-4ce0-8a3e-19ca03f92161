"use client";
import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { isAuthenticated, isReceptionUser, isRestrictedPage } from "@/utils/auth";

export default function ProtectedRoute({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!isAuthenticated() && pathname !== "/authentication/login") {
      router.push("/authentication/login");
      return;
    }

    // Redirect reception users from restricted pages to classes page
    if (isAuthenticated() && isReceptionUser() && isRestrictedPage(pathname)) {
      router.push("/class-management/classes");
    }
  }, [pathname, router]);

  if (!isAuthenticated() && pathname !== "/authentication/login") {
    return null;
  }

  // Don't render restricted pages for reception users
  if (isAuthenticated() && isReceptionUser() && isRestrictedPage(pathname)) {
    return null;
  }

  return <>{children}</>;
}