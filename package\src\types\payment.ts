export interface Payment {
  id: string;
  studentId: string;
  amount: number;
  date: string;
  status: 'paid' | 'unpaid';
  method: 'cash' | 'card' | 'transfer';
  description: string;
}

export interface PaymentWithStudent extends Payment {
  student: {
    id: string;
    name: string;
    phone: string;
  };
}

export interface CreatePaymentInput {
  studentId: string;
  amount: number;
  date: string;
  status: 'paid' | 'unpaid';
  method: 'cash' | 'card' | 'transfer';
  description: string;
}

export interface UpdatePaymentInput extends Partial<CreatePaymentInput> {
  id: string;
}