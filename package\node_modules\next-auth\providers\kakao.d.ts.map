{"version": 3, "file": "kakao.d.ts", "sourceRoot": "", "sources": ["../src/providers/kakao.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,GAAG,CAAA;AAErD,oBAAY,QAAQ,GAAG,MAAM,CAAA;AAC7B,oBAAY,MAAM,GAAG,QAAQ,GAAG,MAAM,CAAA;AACtC,oBAAY,QAAQ,GAChB,KAAK,GACL,OAAO,GACP,OAAO,GACP,OAAO,GACP,OAAO,GACP,OAAO,GACP,OAAO,GACP,OAAO,GACP,OAAO,GACP,OAAO,GACP,KAAK,CAAA;AAET;;;GAGG;AACH,MAAM,WAAW,YAAa,SAAQ,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IACvD,EAAE,EAAE,MAAM,CAAA;IACV,aAAa,CAAC,EAAE,OAAO,CAAA;IACvB,YAAY,CAAC,EAAE,QAAQ,CAAA;IACvB,UAAU,CAAC,EAAE,QAAQ,CAAA;IACrB,UAAU,CAAC,EAAE;QACX,EAAE,CAAC,EAAE,MAAM,CAAA;QACX,MAAM,CAAC,EAAE,MAAM,CAAA;QACf,aAAa,CAAC,EAAE,QAAQ,CAAA;QACxB,WAAW,CAAC,EAAE,OAAO,CAAA;QACrB,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,aAAa,CAAC,EAAE,MAAM,CAAA;QACtB,eAAe,CAAC,EAAE,MAAM,CAAA;KACzB,CAAA;IACD,aAAa,CAAC,EAAE;QACd,uBAAuB,CAAC,EAAE,OAAO,CAAA;QACjC,gCAAgC,CAAC,EAAE,OAAO,CAAA;QAC1C,6BAA6B,CAAC,EAAE,OAAO,CAAA;QACvC,OAAO,CAAC,EAAE;YACR,QAAQ,CAAC,EAAE,MAAM,CAAA;YACjB,mBAAmB,CAAC,EAAE,MAAM,CAAA;YAC5B,iBAAiB,CAAC,EAAE,MAAM,CAAA;YAC1B,gBAAgB,CAAC,EAAE,OAAO,CAAA;SAC3B,CAAA;QACD,oBAAoB,CAAC,EAAE,OAAO,CAAA;QAC9B,IAAI,CAAC,EAAE,MAAM,CAAA;QACb,qBAAqB,CAAC,EAAE,OAAO,CAAA;QAC/B,cAAc,CAAC,EAAE,OAAO,CAAA;QACxB,iBAAiB,CAAC,EAAE,OAAO,CAAA;QAC3B,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,yBAAyB,CAAC,EAAE,OAAO,CAAA;QACnC,SAAS,CAAC,EAAE,QAAQ,CAAA;QACpB,yBAAyB,CAAC,EAAE,OAAO,CAAA;QACnC,SAAS,CAAC,EAAE,MAAM,CAAA;QAClB,wBAAwB,CAAC,EAAE,OAAO,CAAA;QAClC,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,aAAa,CAAC,EAAE,MAAM,CAAA;QACtB,sBAAsB,CAAC,EAAE,OAAO,CAAA;QAChC,MAAM,CAAC,EAAE,MAAM,CAAA;QACf,4BAA4B,CAAC,EAAE,OAAO,CAAA;QACtC,YAAY,CAAC,EAAE,MAAM,CAAA;QACrB,kBAAkB,CAAC,EAAE,OAAO,CAAA;QAC5B,EAAE,CAAC,EAAE,MAAM,CAAA;QACX,mBAAmB,CAAC,EAAE,QAAQ,CAAA;KAC/B,CAAA;CACF;AAED,MAAM,CAAC,OAAO,UAAU,KAAK,CAAC,CAAC,SAAS,YAAY,EAClD,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,GAC1B,WAAW,CAAC,CAAC,CAAC,CAqBhB"}