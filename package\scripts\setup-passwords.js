const bcrypt = require('bcryptjs');

// Default secure passwords - CHANGE THESE IMMEDIATELY
const defaultPasswords = {
  '<EMAIL>': 'SecureParviz2025!',
  '<EMAIL>': 'SecureReception2025!',
  '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@innovative.com': 'SecureMukhammadkhon2025!'
};

async function generatePasswordHashes() {
  console.log('🔐 Generating secure password hashes...\n');
  
  for (const [email, password] of Object.entries(defaultPasswords)) {
    const hash = await bcrypt.hash(password, 12);
    const envVarName = email.split('@')[0].toUpperCase();
    
    console.log(`# ${email}`);
    console.log(`ADMIN_${envVarName}_PASSWORD_HASH=${hash}`);
    console.log(`# Default password: ${password}`);
    console.log('');
  }
  
  console.log('⚠️  IMPORTANT SECURITY NOTES:');
  console.log('1. Copy the hashes above to your .env.local file');
  console.log('2. CHANGE THE DEFAULT PASSWORDS IMMEDIATELY');
  console.log('3. Never commit .env.local to version control');
  console.log('4. Use strong, unique passwords for each account');
  console.log('5. Consider implementing 2FA for additional security');
}

generatePasswordHashes().catch(console.error);
