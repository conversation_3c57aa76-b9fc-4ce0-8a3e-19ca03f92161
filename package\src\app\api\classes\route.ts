import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (id) {
      const classData = await prisma.class.findUnique({
        where: { id },
        include: {
          teacher: true,
          cabinet: true,
          students: true,
        },
      });

      if (!classData) {
        return NextResponse.json({ error: 'Class not found' }, { status: 404 });
      }

      return NextResponse.json(classData);
    }

    const classes = await prisma.class.findMany({
      include: {
        teacher: true,
        cabinet: true,
        students: true,
      },
    });
    return NextResponse.json(classes);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch classes' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { teacherId, cabinetId, students, ...classData } = body;

    const newClass = await prisma.class.create({
      data: {
        ...classData,
        teacher: { connect: { id: teacherId } },
        cabinet: { connect: { id: cabinetId } },
        students: {
          connect: students?.map((id: string) => ({ id })) || [],
        },
      },
      include: {
        teacher: true,
        cabinet: true,
        students: true,
      },
    });

    return NextResponse.json(newClass, { status: 201 });
  } catch (error) {
    console.error('Create class error:', error);
    return NextResponse.json({ error: 'Failed to create class' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ error: 'Class ID is required' }, { status: 400 });
    }

    const body = await request.json();
    const { teacherId, cabinetId, students, ...classData } = body;

    const updatedClass = await prisma.class.update({
      where: { id },
      data: {
        ...classData,
        teacher: teacherId ? { connect: { id: teacherId } } : undefined,
        cabinet: cabinetId ? { connect: { id: cabinetId } } : undefined,
        students: students ? {
          set: students.map((id: string) => ({ id })),
        } : undefined,
      },
      include: {
        teacher: true,
        cabinet: true,
        students: true,
      },
    });

    return NextResponse.json(updatedClass);
  } catch (error) {
    console.error('Update class error:', error);
    return NextResponse.json({ error: 'Failed to update class' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ error: 'Class ID is required' }, { status: 400 });
    }

    await prisma.class.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'Class deleted successfully' });
  } catch (error) {
    console.error('Delete class error:', error);
    return NextResponse.json({ error: 'Failed to delete class' }, { status: 500 });
  }
}