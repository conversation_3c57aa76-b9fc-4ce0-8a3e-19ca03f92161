import { prisma } from '@/lib/prisma';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const students = await prisma.student.findMany({
      include: {
        classes: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
    return NextResponse.json(students);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch students' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const student = await prisma.student.create({
      data: {
        name: body.name,
        phone: body.phone,
        joinDate: new Date(body.joinDate),
        paymentStatus: body.paymentStatus || 'unpaid',
        ...(body.classIds && {
          classes: {
            connect: body.classIds.map((id: string) => ({ id })),
          },
        }),
      },
      include: {
        classes: true,
      },
    });
    return NextResponse.json(student);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create student' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const { id, ...data } = body;

    const student = await prisma.student.update({
      where: { id },
      data: {
        name: data.name,
        phone: data.phone,
        joinDate: new Date(data.joinDate),
        paymentStatus: data.paymentStatus || 'unpaid',
        ...(data.classIds && {
          classes: {
            set: data.classIds.map((id: string) => ({ id })),
          },
        }),
      },
      include: {
        classes: true,
      },
    });
    return NextResponse.json(student);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to update student' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ error: 'Student ID is required' }, { status: 400 });
    }

    await prisma.student.delete({
      where: { id },
    });
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to delete student' }, { status: 500 });
  }
}