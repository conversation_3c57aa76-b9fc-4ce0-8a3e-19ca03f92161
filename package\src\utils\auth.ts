interface Admin {
  email: string;
  password: string;
}

const ADMINS: Admin[] = [
  { email: '<EMAIL>', password: 'Parviz0106$' },
  { email: '<EMAIL>', password: 'Reception123-' },
  { email: '<PERSON><PERSON><EMAIL>', password: 'Mukhammadkhon0106$' }
];

export const authenticate = (email: string, password: string): boolean => {
  const admin = ADMINS.find(a => a.email === email && a.password === password);
  if (admin) {
    localStorage.setItem('isAuthenticated', 'true');
    localStorage.setItem('userEmail', admin.email);
    return true;
  }
  return false;
};

export const isAuthenticated = (): boolean => {
  if (typeof window === 'undefined') return false;
  return localStorage.getItem('isAuthenticated') === 'true';
};

export const logout = (): void => {
  localStorage.removeItem('isAuthenticated');
  localStorage.removeItem('userEmail');
};

export const getCurrentUser = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('userEmail');
};

export const isReceptionUser = (): boolean => {
  const userEmail = getCurrentUser();
  return userEmail === '<EMAIL>';
};

export const isDashboardUser = (): boolean => {
  const userEmail = getCurrentUser();
  return userEmail === '<EMAIL>' || userEmail === '<EMAIL>';
};

export const isRestrictedPage = (pathname: string): boolean => {
  const restrictedPages = [
    '/',  // Dashboard
    '/class-management/teachers',
    '/class-management/payments'
  ];
  // Only apply restrictions for reception user
  return isReceptionUser() && restrictedPages.includes(pathname);
};