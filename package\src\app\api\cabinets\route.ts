import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (id) {
      const cabinet = await prisma.cabinet.findUnique({
        where: { id },
        include: {
          classes: true,
        },
      });

      if (!cabinet) {
        return NextResponse.json({ error: 'Cabinet not found' }, { status: 404 });
      }

      return NextResponse.json(cabinet);
    }

    const cabinets = await prisma.cabinet.findMany({
      include: {
        classes: true,
      },
    });
    return NextResponse.json(cabinets);
  } catch (error) {
    console.error('Failed to fetch cabinets:', error);
    return NextResponse.json({ error: 'Failed to fetch cabinets' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const newCabinet = await prisma.cabinet.create({
      data: body,
      include: {
        classes: true,
      },
    });

    return NextResponse.json(newCabinet, { status: 201 });
  } catch (error) {
    console.error('Failed to create cabinet:', error);
    return NextResponse.json({ error: 'Failed to create cabinet' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ error: 'Cabinet ID is required' }, { status: 400 });
    }

    const body = await request.json();

    const updatedCabinet = await prisma.cabinet.update({
      where: { id },
      data: body,
      include: {
        classes: true,
      },
    });

    return NextResponse.json(updatedCabinet);
  } catch (error) {
    console.error('Failed to update cabinet:', error);
    return NextResponse.json({ error: 'Failed to update cabinet' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ error: 'Cabinet ID is required' }, { status: 400 });
    }

    // Check if cabinet has any classes
    const cabinet = await prisma.cabinet.findUnique({
      where: { id },
      include: {
        classes: true,
      },
    });

    if (cabinet?.classes.length) {
      return NextResponse.json(
        { error: 'Cannot delete cabinet with assigned classes' },
        { status: 400 }
      );
    }

    await prisma.cabinet.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'Cabinet deleted successfully' });
  } catch (error) {
    console.error('Failed to delete cabinet:', error);
    return NextResponse.json({ error: 'Failed to delete cabinet' }, { status: 500 });
  }
}