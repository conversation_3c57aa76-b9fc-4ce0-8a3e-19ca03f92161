'use client';
import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Box,
  Typography,
  IconButton,
  CircularProgress,
} from '@mui/material';
import { ClassWithRelations } from '@/types';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { Prisma } from '@prisma/client';

type TeacherWithoutClasses = Prisma.TeacherGetPayload<{
  select: {
    id: true;
    name: true;
    email: true;
    phone: true;
    subjects: true;
    qualifications: true;
    joinDate: true;
    status: true;
  };
}>;

type CabinetWithoutClasses = Prisma.CabinetGetPayload<{
  select: {
    id: true;
    name: true;
    capacity: true;
    equipment: true;
    status: true;
    location: true;
  };
}>;

interface ClassFormData {
  id?: string;
  name: string;
  subject: string;
  level: string;
  stage: 'EARLY' | 'MIDDLE' | 'LATE';
  language: 'RUSSIAN' | 'UZBEK' | 'MIXED';
  schedule: Array<{
    day: string;
    startTime: string;
    endTime: string;
  }>;
  courseAmount: number;
  students: any[];
  teacherId?: string;
  cabinetId?: string;
  openingDate?: Date | null;
  createdAt?: Date;
}

interface ClassDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (classData: Partial<ClassWithRelations>) => void;
  onDelete?: () => void;
  classData?: ClassWithRelations;
  mode: 'create' | 'edit';
}

const defaultSchedule = {
  day: 'Monday',
  startTime: '09:00',
  endTime: '10:30',
};

const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
const levels = ['A1', 'A2', 'B1', 'B2', 'IELTS', 'Math', 'IT', 'SAT', 'Individual', 'Speaking', 'Kids'];
const stages = ['EARLY', 'MIDDLE', 'LATE'];
const languages = ['RUSSIAN', 'UZBEK', 'MIXED'];

const ClassDialog = ({ open, onClose, onSave, onDelete, classData, mode }: ClassDialogProps) => {
  const [formData, setFormData] = useState<ClassFormData>({
    name: '',
    subject: 'English',
    level: 'A1',
    stage: 'EARLY',
    language: 'RUSSIAN',
    schedule: [defaultSchedule],
    courseAmount: 500000,
    students: [],
  });

  const [teachers, setTeachers] = useState<TeacherWithoutClasses[]>([]);
  const [cabinets, setCabinets] = useState<CabinetWithoutClasses[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchTeachersAndCabinets = async () => {
    try {
      const [teachersRes, cabinetsRes] = await Promise.all([
        fetch('/api/teachers'),
        fetch('/api/cabinets')
      ]);

      if (!teachersRes.ok || !cabinetsRes.ok) {
        throw new Error('Failed to fetch data');
      }

      const teachersData = await teachersRes.json();
      const cabinetsData = await cabinetsRes.json();

      setTeachers(teachersData);
      setCabinets(cabinetsData);
    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchTeachersAndCabinets();
    }
  }, [open]);

  useEffect(() => {
    if (classData && mode === 'edit') {
      setFormData({
        id: classData.id,
        name: classData.name,
        subject: classData.subject,
        level: classData.level,
        stage: classData.stage || 'EARLY',
        language: classData.language || 'RUSSIAN',
        schedule: classData.schedule as any[],
        courseAmount: classData.courseAmount,
        students: classData.students,
        teacherId: classData.teacher.id,
        cabinetId: classData.cabinet.id,
        openingDate: classData.openingDate,
        createdAt: classData.createdAt,
      });
    } else {
      setFormData({
        name: '',
        subject: 'English',
        level: 'A1',
        stage: 'EARLY',
        language: 'RUSSIAN',
        schedule: [defaultSchedule],
        courseAmount: 500000,
        students: [],
      });
    }
  }, [classData, mode]);

  const handleChange = (field: keyof ClassFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleScheduleChange = (index: number, field: keyof typeof defaultSchedule, value: string) => {
    const newSchedule = [...formData.schedule];
    newSchedule[index] = { ...newSchedule[index], [field]: value };
    handleChange('schedule', newSchedule);
  };

  const addSchedule = () => {
    handleChange('schedule', [...formData.schedule, defaultSchedule]);
  };

  const removeSchedule = (index: number) => {
    const newSchedule = [...formData.schedule];
    newSchedule.splice(index, 1);
    handleChange('schedule', newSchedule);
  };

  const handleSubmit = () => {
    const selectedTeacher = teachers.find(t => t.id === formData.teacherId);
    const selectedCabinet = cabinets.find(c => c.id === formData.cabinetId);

    if (!selectedTeacher || !selectedCabinet) {
      return; // Handle error - teacher and cabinet are required
    }

    const submissionData: Partial<ClassWithRelations> = {
      id: formData.id,
      name: formData.name,
      subject: formData.subject,
      level: formData.level,
      stage: formData.stage,
      language: formData.language,
      schedule: formData.schedule,
      courseAmount: formData.courseAmount,
      students: formData.students,
      teacher: selectedTeacher,
      cabinet: selectedCabinet,
      openingDate: formData.openingDate,
      createdAt: formData.createdAt,
    };

    if (mode === 'create') {
      submissionData.createdAt = new Date();
      submissionData.openingDate = new Date();
    }

    onSave(submissionData);
  };

  if (loading) {
    return (
      <Dialog open={open} onClose={onClose}>
        <DialogContent>
          <Box display="flex" justifyContent="center" p={3}>
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'create' ? 'Create New Class' : 'Edit Class'}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Class Name"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Teacher</InputLabel>
              <Select
                value={formData.teacherId || ''}
                label="Teacher"
                onChange={(e) => handleChange('teacherId', e.target.value)}
              >
                {teachers.map((teacher) => (
                  <MenuItem key={teacher.id} value={teacher.id}>
                    {teacher.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Level</InputLabel>
              <Select
                value={formData.level}
                label="Level"
                onChange={(e) => handleChange('level', e.target.value)}
              >
                {levels.map((level) => (
                  <MenuItem key={level} value={level}>
                    {level}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Stage</InputLabel>
              <Select
                value={formData.stage}
                label="Stage"
                onChange={(e) => handleChange('stage', e.target.value)}
              >
                {stages.map((stage) => (
                  <MenuItem key={stage} value={stage}>
                    {stage}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Language</InputLabel>
              <Select
                value={formData.language}
                label="Language"
                onChange={(e) => handleChange('language', e.target.value)}
              >
                {languages.map((language) => (
                  <MenuItem key={language} value={language}>
                    {language}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Cabinet</InputLabel>
              <Select
                value={formData.cabinetId || ''}
                label="Cabinet"
                onChange={(e) => handleChange('cabinetId', e.target.value)}
              >
                {cabinets.map((cabinet) => (
                  <MenuItem key={cabinet.id} value={cabinet.id}>
                    {cabinet.name} ({cabinet.capacity} seats)
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              type="number"
              label="Course Amount (UZS)"
              value={formData.courseAmount}
              onChange={(e) => handleChange('courseAmount', Number(e.target.value))}
            />
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Schedule
              </Typography>
              {formData.schedule.map((schedule, index) => (
                <Box key={index} sx={{ display: 'flex', gap: 2, mb: 2, alignItems: 'center' }}>
                  <FormControl sx={{ minWidth: 120 }}>
                    <InputLabel>Day</InputLabel>
                    <Select
                      value={schedule.day}
                      label="Day"
                      onChange={(e) => handleScheduleChange(index, 'day', e.target.value)}
                    >
                      {days.map((day) => (
                        <MenuItem key={day} value={day}>
                          {day}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <TextField
                    label="Start Time"
                    type="time"
                    value={schedule.startTime}
                    onChange={(e) => handleScheduleChange(index, 'startTime', e.target.value)}
                    InputLabelProps={{ shrink: true }}
                    inputProps={{ step: 300 }}
                  />
                  <TextField
                    label="End Time"
                    type="time"
                    value={schedule.endTime}
                    onChange={(e) => handleScheduleChange(index, 'endTime', e.target.value)}
                    InputLabelProps={{ shrink: true }}
                    inputProps={{ step: 300 }}
                  />
                  <IconButton 
                    color="error" 
                    onClick={() => removeSchedule(index)}
                    disabled={formData.schedule.length === 1}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ))}
              <Button
                startIcon={<AddIcon />}
                onClick={addSchedule}
                variant="outlined"
                size="small"
              >
                Add Schedule
              </Button>
            </Box>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        {mode === 'edit' && onDelete && (
          <Button onClick={onDelete} color="error" sx={{ mr: 'auto' }}>
            Delete Class
          </Button>
        )}
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained">
          {mode === 'create' ? 'Create' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ClassDialog;