generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Stage {
  EARLY
  MIDDLE
  LATE
}

enum Language {
  RUSSIAN
  UZBEK
  MIXED
}

model User {
  id                String    @id @default(cuid())
  email            String    @unique
  password         String
  role             String    @default("USER")
  name             String
  lastLoginAt      DateTime?
  failedAttempts   Int       @default(0)
  lastFailedAttempt DateTime?
  isLocked         Boolean   @default(false)
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
}

model Teacher {
  id             String   @id @default(cuid())
  name           String
  email          String   @unique
  phone          String
  subjects       String[]
  qualifications String[]
  joinDate       DateTime
  status         String
  classes        Class[]
}

model Student {
  id            String    @id @default(cuid())
  name          String
  phone         String
  joinDate      DateTime
  paymentStatus String
  classes       Class[]
  payments      Payment[]
}

model Class {
  id           String    @id @default(cuid())
  name         String
  teacher      Teacher   @relation(fields: [teacherId], references: [id])
  teacherId    String
  subject      String
  level        String
  stage        Stage     @default(EARLY)
  language     Language  @default(RUSSIAN)
  cabinet      Cabinet   @relation(fields: [cabinetId], references: [id])
  cabinetId    String
  schedule     Json[]    // Storing schedule as JSON array
  students     Student[]
  courseAmount Float
  createdAt    DateTime  @default(now())
  openingDate  DateTime?
}

model Cabinet {
  id        String  @id @default(cuid())
  name      String
  capacity  Int
  equipment String[]
  status    String
  location  String
  classes   Class[]
}

model Payment {
  id          String   @id @default(cuid())
  student     Student  @relation(fields: [studentId], references: [id])
  studentId   String
  amount      Float
  date        DateTime
  status      String
  method      String
  description String
}