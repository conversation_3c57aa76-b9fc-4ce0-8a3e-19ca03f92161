# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-change-this-immediately-to-a-random-64-character-string
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/innovative_centre_db

# Admin Credentials (Hashed - CHANGE DEFAULT PASSWORDS IMMEDIATELY)
ADMIN_PARVIZ_EMAIL=<EMAIL>
ADMIN_PARVIZ_PASSWORD_HASH=$2b$12$9Mit8.FgUnuHYLWND7ysqOf6RoeUnA89rGG7Lo2f24Ym60OdGeAVm
# Default password: SecureParviz2025! - CHANGE THIS!

ADMIN_RECEPTION_EMAIL=<EMAIL>
ADMIN_RECEPTION_PASSWORD_HASH=$2b$12$BMxQXi.nRdFgsAyKGn8Aw.LJcYF6TO.wmReh3znI2OGlSRr3C416a
# Default password: SecureReception2025! - CHAN<PERSON> THIS!

ADMIN_MUKHAMMADKHON_EMAIL=<EMAIL>
ADMIN_MUKHAMMADKHON_PASSWORD_HASH=$2b$12$CxO1qGWZpgcvzoAJp1ERs.NwswKhjVRxxe56Xtuvq1uuaBwpZekmS
# Default password: SecureMukhammadkhon2025! - CHANGE THIS!

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security Headers
SECURITY_HEADERS_ENABLED=true

# Audit Logging
AUDIT_LOG_ENABLED=true
AUDIT_LOG_LEVEL=info
