import {
  IconAperture,
  IconCopy,
  IconLayoutDashboard,
  IconMoodHappy,
  IconTypography,
  IconSchool,
  IconUsers,
  IconCalendar,
  IconCash,
  IconDoor,
  IconChalkboard,
} from "@tabler/icons-react";

import { uniqueId } from "lodash";

const Menuitems = [
  {
    id: uniqueId(),
    title: "Главная",
    icon: IconLayoutDashboard,
    href: "/",
  },
  {
    navlabel: true,
    subheader: "Управление классами",
  },
  {
    id: uniqueId(),
    title: "Группы",
    icon: IconSchool,
    href: "/class-management/classes",
  },
  {
    id: uniqueId(),
    title: "Студенты",
    icon: IconUsers,
    href: "/class-management/students",
  },
  {
    id: uniqueId(),
    title: "Учителя",
    icon: IconChalkboard,
    href: "/class-management/teachers",
  },
  {
    id: uniqueId(),
    title: "Расписание",
    icon: IconCalendar,
    href: "/class-management/timetable",
  },
  {
    id: uniqueId(),
    title: "Кабинеты",
    icon: IconDoor,
    href: "/class-management/cabinets",
  },
  {
    id: uniqueId(),
    title: "Оплата",
    icon: IconCash,
    href: "/class-management/payments",
  },
];

export default Menuitems;
