{"version": 3, "file": "oauth.d.ts", "sourceRoot": "", "sources": ["../src/providers/oauth.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,qBAAqB,EAAE,MAAM,cAAc,CAAA;AACzD,OAAO,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,IAAI,CAAA;AAE5D,OAAO,KAAK,EACV,uBAAuB,EACvB,kBAAkB,EAClB,MAAM,EACN,cAAc,EACd,cAAc,EACd,mBAAmB,EACnB,oBAAoB,EACpB,WAAW,EACZ,MAAM,eAAe,CAAA;AACtB,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,MAAM,CAAA;AAE/B,aAAK,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;AAE5C,YAAY,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAA;AAEtD,aAAK,UAAU,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,OAAO,CAAA;AAErD,oBAAY,WAAW,GAAG,oBAAoB,GAAG,mBAAmB,CAAA;AAEpE,aAAK,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,GAAG,QAAQ,CAAC,CAAC,CAAA;AAE9E,aAAK,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;AAExC,aAAK,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAC9B,OAAO,EAAE,CAAC,GAAG;IACX,6BAA6B;IAC7B,MAAM,EAAE,MAAM,CAAA;IACd,+EAA+E;IAC/E,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG;QACzB,SAAS,EAAE,MAAM,CAAA;QACjB,WAAW,EAAE,MAAM,CAAA;KACpB,CAAA;CACF,KACE,SAAS,CAAC,CAAC,CAAC,CAAA;AAEjB,kEAAkE;AAClE,UAAU,uBAAuB,CAAC,CAAC,SAAS,SAAS,EAAE,CAAC,EAAE,CAAC;IACzD,6EAA6E;IAC7E,GAAG,CAAC,EAAE,MAAM,CAAA;IACZ,2CAA2C;IAC3C,MAAM,CAAC,EAAE,CAAC,CAAA;IACV;;;;;;;OAOG;IACH,OAAO,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;CACnC;AAED,6FAA6F;AAC7F,oBAAY,eAAe,CACzB,CAAC,SAAS,SAAS,EACnB,CAAC,GAAG,GAAG,EACP,CAAC,GAAG,GAAG,IACL,uBAAuB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;AAEpC,oBAAY,4BAA4B,GACtC,eAAe,CAAC,uBAAuB,CAAC,CAAA;AAE1C,oBAAY,oBAAoB,GAAG,eAAe,CAChD,SAAS,EACT;IACE;;;OAGG;IACH,MAAM,EAAE,kBAAkB,CAAA;IAC1B;;;OAGG;IACH,MAAM,EAAE,WAAW,CAAA;CACpB,EACD;IACE,MAAM,EAAE,QAAQ,CAAA;CACjB,CACF,CAAA;AAED,oBAAY,uBAAuB,GAAG,eAAe,CACnD,SAAS,EACT;IAAE,MAAM,EAAE,QAAQ,CAAA;CAAE,EACpB,OAAO,CACR,CAAA;AAED,MAAM,WAAW,yBAAyB;IACxC,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,EAAE,EAAE,MAAM,CAAA;IACV,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,CAAC,EAAE,MAAM,CAAA;CAClB;AAED,MAAM,WAAW,WAAW,CAAC,CAAC,CAAE,SAAQ,qBAAqB,EAAE,aAAa;IAC1E;;;;;;;;OAQG;IACH,SAAS,CAAC,EAAE,MAAM,CAAA;IAClB,aAAa,CAAC,EAAE,MAAM,CAAA;IACtB;;;;OAIG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,4BAA4B,CAAA;IACrD,KAAK,CAAC,EAAE,MAAM,GAAG,oBAAoB,CAAA;IACrC,QAAQ,CAAC,EAAE,MAAM,GAAG,uBAAuB,CAAA;IAC3C,IAAI,EAAE,OAAO,CAAA;IACb,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,OAAO,EAAE,CAAC,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA;IAC1D,MAAM,CAAC,EAAE,UAAU,GAAG,UAAU,EAAE,CAAA;IAClC,MAAM,CAAC,EAAE,OAAO,CAAC,cAAc,CAAC,CAAA;IAChC,IAAI,CAAC,EAAE;QAAE,IAAI,EAAE,GAAG,EAAE,CAAA;KAAE,CAAA;IACtB,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,YAAY,CAAC,EAAE,MAAM,CAAA;IACrB;;;;;;;;OAQG;IACH,OAAO,CAAC,EAAE,OAAO,CAAA;IAEjB,MAAM,CAAC,EAAE,MAAM,CAAA;IAEf,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,yGAAyG;IACzG,WAAW,CAAC,EAAE,WAAW,CAAA;IAEzB,KAAK,CAAC,EAAE,yBAAyB,CAAA;IAEjC;;;;OAIG;IACH,OAAO,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAA;IAG5B,cAAc,CAAC,EAAE,MAAM,CAAA;IACvB,eAAe,CAAC,EAAE,MAAM,CAAA;IACxB,UAAU,CAAC,EAAE,MAAM,CAAA;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,iCAAiC,CAAC,EAAE,OAAO,CAAA;CAC5C;AAED,oBAAY,eAAe,CAAC,CAAC,IAAI,IAAI,CACnC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EACvB,SAAS,GAAG,MAAM,CACnB,GACC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,UAAU,GAAG,cAAc,CAAC,CAAC,CAAA;AAE7D,oBAAY,aAAa,GAAG,CAC1B,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAC/B,WAAW,CAAC,GAAG,CAAC,CAAA"}