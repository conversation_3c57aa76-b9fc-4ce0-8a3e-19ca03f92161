'use client';
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Card, CardContent, Typography, Button, Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, IconButton, Snackbar, Alert, Select, MenuItem, styled, SelectChangeEvent, CircularProgress, Dialog, DialogContent } from '@mui/material';
import PageContainer from '@/app/(DashboardLayout)/components/container/PageContainer';
import { Teacher } from '@/types/teacher';
import { IconEdit, IconTrash, IconMaximize, IconMinimize } from '@tabler/icons-react';
import CabinetDialog from './CabinetDialog';
import { CabinetWithClasses, ClassWithRelations } from '@/types';

type CabinetStatus = 'available' | 'occupied' | 'maintenance';
type CabinetWithStatus = Omit<CabinetWithClasses, 'status'> & {
  status: CabinetStatus;
};

// Styled components for uniform grid
const StyledTableCell = styled(TableCell)({
  width: '150px',
  padding: '8px',
  textAlign: 'center',
  '&.time-cell': {
    width: '80px',
    position: 'sticky',
    left: 0,
    zIndex: 2,
    backgroundColor: '#fff', // Add background color to prevent content showing through
  },
  '&.mwf': {
    backgroundColor: '#f5f5f5',
  },
  '&.tts': {
    backgroundColor: '#e8f4fd',
  },
});

const StyledSelect = styled(Select<string>)({
  width: '100%',
  '& .MuiSelect-select': {
    padding: '8px',
  },
});

type ScheduleType = 'MWF' | 'TTS';

const Cabinets = () => {
  const [cabinets, setCabinets] = useState<CabinetWithStatus[]>([]);
  const [selectedCabinet, setSelectedCabinet] = useState<CabinetWithStatus | undefined>(undefined);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });
  
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [classes, setClasses] = useState<ClassWithRelations[]>([]);
  const [scheduleFullscreen, setScheduleFullscreen] = useState(false);
  
  const toggleScheduleFullscreen = () => {
    setScheduleFullscreen(!scheduleFullscreen);
  };

  const fetchTeachers = async () => {
    try {
      const response = await fetch('/api/teachers');
      if (!response.ok) {
        throw new Error('Failed to fetch teachers');
      }
      const data = await response.json();
      setTeachers(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch teachers');
      setNotification({
        open: true,
        message: 'Failed to fetch teachers',
        severity: 'error',
      });
    }
  };

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/classes');
      if (!response.ok) {
        throw new Error('Failed to fetch classes');
      }
      const data = await response.json();
      setClasses(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch classes');
      setNotification({
        open: true,
        message: 'Failed to fetch classes',
        severity: 'error',
      });
    }
  };

  useEffect(() => {
    fetchCabinets();
    fetchTeachers();
    fetchClasses();
  }, []);

  const fetchCabinets = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/cabinets');
      if (!response.ok) {
        throw new Error('Failed to fetch cabinets');
      }
      const data = await response.json();
      // Ensure the status is of type CabinetStatus
      const typedData = data.map((cabinet: CabinetWithClasses) => ({
        ...cabinet,
        status: cabinet.status as CabinetStatus
      }));
      setCabinets(typedData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while fetching cabinets');
      setNotification({
        open: true,
        message: 'Failed to fetch cabinets',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: CabinetStatus) => {
    switch (status) {
      case 'available':
        return 'success';
      case 'occupied':
        return 'warning';
      case 'maintenance':
        return 'error';
      default:
        return 'default';
    }
  };

  // Generate time slots from 9:00 to 20:00 with 30-minute intervals
  const timeSlots = Array.from({ length: 23 }, (_, i) => {
    const hour = Math.floor(i / 2) + 9;
    const minutes = i % 2 === 0 ? '00' : '30';
    return `${hour.toString().padStart(2, '0')}:${minutes}`;
  });

  const handleCreateCabinet = () => {
    setSelectedCabinet(undefined);
    setDialogMode('create');
    setDialogOpen(true);
  };

  const handleEditCabinet = (cabinet: CabinetWithStatus) => {
    setSelectedCabinet(cabinet);
    setDialogMode('edit');
    setDialogOpen(true);
  };

  const handleDeleteCabinet = async (cabinetId: string) => {
    try {
      const response = await fetch(`/api/cabinets?id=${cabinetId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete cabinet');
      }

      setCabinets(prev => prev.filter(cabinet => cabinet.id !== cabinetId));
      setDialogOpen(false); // Close dialog if open
      setNotification({
        open: true,
        message: 'Cabinet deleted successfully',
        severity: 'success',
      });
    } catch (error) {
      setNotification({
        open: true,
        message: error instanceof Error ? error.message : 'Failed to delete cabinet',
        severity: 'error',
      });
    }
  };

  const handleSaveCabinet = async (cabinetData: Partial<CabinetWithStatus>) => {
    try {
      let response;
      if (dialogMode === 'create') {
        response = await fetch('/api/cabinets', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(cabinetData),
        });
      } else {
        response = await fetch(`/api/cabinets?id=${cabinetData.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(cabinetData),
        });
      }

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || `Failed to ${dialogMode} cabinet`);
      }

      const updatedCabinet = await response.json();
      
      if (dialogMode === 'create') {
        setCabinets(prev => [...prev, updatedCabinet]);
      } else {
        setCabinets(prev =>
          prev.map(cabinet =>
            cabinet.id === updatedCabinet.id ? updatedCabinet : cabinet
          )
        );
      }

      setDialogOpen(false);
      setNotification({
        open: true,
        message: `Cabinet ${dialogMode}d successfully`,
        severity: 'success',
      });
    } catch (error) {
      setNotification({
        open: true,
        message: error instanceof Error ? error.message : `Failed to ${dialogMode} cabinet`,
        severity: 'error',
      });
    }
  };

  // This function has been removed as we're now using automatic assignment

  // Helper function to check if a time is within a range
  const isTimeInRange = (time: string, startTime: string, endTime: string): boolean => {
    // Convert times to minutes for easier comparison
    const convertToMinutes = (timeStr: string): number => {
      const [hours, minutes] = timeStr.split(':').map(Number);
      return hours * 60 + minutes;
    };
    
    const timeMinutes = convertToMinutes(time);
    const startMinutes = convertToMinutes(startTime);
    const endMinutes = convertToMinutes(endTime);
    
    // Check if time is within the range (inclusive of start, exclusive of end)
    return timeMinutes >= startMinutes && timeMinutes < endMinutes;
  };

  // Function to get cabinet assignments from class data
  const getTeacherForSlot = (time: string, cabinetId: string, scheduleType: ScheduleType): string => {
    if (!classes || classes.length === 0) return '';
    
    // Find classes that use this cabinet at this time on these days
    const matchingClass = classes.find((classInfo: ClassWithRelations) => {
      // Check if class uses this cabinet
      if (classInfo.cabinet.id !== cabinetId) return false;
      
      // Get schedule information
      const schedule = classInfo.schedule as { day: string; startTime: string; endTime: string; }[];
      
      // Check if class is scheduled at this time on relevant days
      return schedule.some(slot => {
        // Check if the time slot is within the class duration
        if (!isTimeInRange(time, slot.startTime, slot.endTime)) return false;
        
        // Check day match based on schedule type
        if (scheduleType === 'MWF') {
          return ['Monday', 'Wednesday', 'Friday'].includes(slot.day);
        } else {
          return ['Tuesday', 'Thursday', 'Saturday'].includes(slot.day);
        }
      });
    });
    
    // Return teacher name if found, empty string otherwise
    return matchingClass ? matchingClass.teacher.name : '';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  return (
    <PageContainer title="Cabinets" description="Manage classroom spaces">
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h3">Cabinets</Typography>
                <Button variant="contained" color="primary" onClick={handleCreateCabinet}>
                  Add New Cabinet
                </Button>
              </Box>
              
              <TableContainer sx={{ maxHeight: 400, overflow: 'auto' }}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Location</TableCell>
                      <TableCell>Capacity</TableCell>
                      <TableCell>Equipment</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {cabinets.map((cabinet) => (
                      <TableRow key={cabinet.id}>
                        <TableCell>{cabinet.name}</TableCell>
                        <TableCell>{cabinet.location}</TableCell>
                        <TableCell>{cabinet.capacity} students</TableCell>
                        <TableCell>
                          {cabinet.equipment.map((item, index) => (
                            <Chip
                              key={index}
                              label={item}
                              size="small"
                              sx={{ mr: 0.5, mb: 0.5 }}
                            />
                          ))}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={cabinet.status}
                            color={getStatusColor(cabinet.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton 
                            color="primary" 
                            size="small" 
                            sx={{ mr: 1 }}
                            onClick={() => handleEditCabinet(cabinet)}
                          >
                            <IconEdit size={18} />
                          </IconButton>
                          <IconButton 
                            color="error" 
                            size="small"
                            onClick={() => handleDeleteCabinet(cabinet.id)}
                          >
                            <IconTrash size={18} />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

            </CardContent>
          </Card>
        </Grid>

        {/* Schedule Section */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h3">Schedule</Typography>
                <IconButton
                  color="primary"
                  onClick={toggleScheduleFullscreen}
                  aria-label="Toggle fullscreen"
                >
                  <IconMaximize size={24} />
                </IconButton>
              </Box>
              <Box sx={{ overflowX: 'auto' }}>
                <TableContainer sx={{ maxHeight: 600 }}>
                  <Table stickyHeader sx={{ minWidth: 'max-content' }}>
                    <TableHead>
                      <TableRow>
                        <StyledTableCell className="time-cell">Time</StyledTableCell>
                        {cabinets.map((cabinet) => (
                          <React.Fragment key={cabinet.id}>
                            <StyledTableCell className="mwf">
                              {cabinet.name} (M/W/F)
                            </StyledTableCell>
                            <StyledTableCell className="tts">
                              {cabinet.name} (T/T/S)
                            </StyledTableCell>
                          </React.Fragment>
                        ))}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {timeSlots.map((time) => (
                        <TableRow key={time} sx={{ height: '52px' }}>
                          <StyledTableCell className="time-cell">{time}</StyledTableCell>
                          {cabinets.map((cabinet) => (
                            <React.Fragment key={`${time}-${cabinet.id}`}>
                              <StyledTableCell className="mwf">
                                {getTeacherForSlot(time, cabinet.id, 'MWF') ? (
                                  <Chip
                                    label={getTeacherForSlot(time, cabinet.id, 'MWF')}
                                    size="small"
                                    color="primary"
                                    sx={{ width: '100%' }}
                                  />
                                ) : (
                                  <Typography variant="body2" color="text.secondary" align="center">
                                    Not assigned
                                  </Typography>
                                )}
                              </StyledTableCell>
                              <StyledTableCell className="tts">
                                {getTeacherForSlot(time, cabinet.id, 'TTS') ? (
                                  <Chip
                                    label={getTeacherForSlot(time, cabinet.id, 'TTS')}
                                    size="small"
                                    color="info"
                                    sx={{ width: '100%' }}
                                  />
                                ) : (
                                  <Typography variant="body2" color="text.secondary" align="center">
                                    Not assigned
                                  </Typography>
                                )}
                              </StyledTableCell>
                            </React.Fragment>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <CabinetDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onSave={handleSaveCabinet}
        onDelete={selectedCabinet ? () => handleDeleteCabinet(selectedCabinet.id) : undefined}
        cabinetData={selectedCabinet}
        mode={dialogMode}
      />

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification({ ...notification, open: false })}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification({ ...notification, open: false })}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Fullscreen Schedule Dialog */}
      <Dialog
        open={scheduleFullscreen}
        onClose={toggleScheduleFullscreen}
        fullScreen
        PaperProps={{
          sx: {
            bgcolor: 'background.default',
            boxShadow: 24,
            pt: 2,
            px: 4,
            pb: 3,
          },
        }}
      >
        <DialogContent sx={{ p: 0 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h3">Schedule</Typography>
            <IconButton
              color="primary"
              onClick={toggleScheduleFullscreen}
              aria-label="Exit fullscreen"
            >
              <IconMinimize size={24} />
            </IconButton>
          </Box>
          <Box sx={{ overflowX: 'auto' }}>
            <TableContainer sx={{ height: 'calc(100vh - 120px)' }}>
              <Table stickyHeader sx={{ minWidth: 'max-content' }}>
                <TableHead>
                  <TableRow>
                    <StyledTableCell className="time-cell">Time</StyledTableCell>
                    {cabinets.map((cabinet) => (
                      <React.Fragment key={cabinet.id}>
                        <StyledTableCell className="mwf">
                          {cabinet.name} (M/W/F)
                        </StyledTableCell>
                        <StyledTableCell className="tts">
                          {cabinet.name} (T/T/S)
                        </StyledTableCell>
                      </React.Fragment>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {timeSlots.map((time) => (
                    <TableRow key={time} sx={{ height: '52px' }}>
                      <StyledTableCell className="time-cell">{time}</StyledTableCell>
                      {cabinets.map((cabinet) => (
                        <React.Fragment key={`${time}-${cabinet.id}`}>
                          <StyledTableCell className="mwf">
                            {getTeacherForSlot(time, cabinet.id, 'MWF') ? (
                              <Chip
                                label={getTeacherForSlot(time, cabinet.id, 'MWF')}
                                size="small"
                                color="primary"
                                sx={{ width: '100%' }}
                              />
                            ) : (
                              <Typography variant="body2" color="text.secondary" align="center">
                                Not assigned
                              </Typography>
                            )}
                          </StyledTableCell>
                          <StyledTableCell className="tts">
                            {getTeacherForSlot(time, cabinet.id, 'TTS') ? (
                              <Chip
                                label={getTeacherForSlot(time, cabinet.id, 'TTS')}
                                size="small"
                                color="info"
                                sx={{ width: '100%' }}
                              />
                            ) : (
                              <Typography variant="body2" color="text.secondary" align="center">
                                Not assigned
                              </Typography>
                            )}
                          </StyledTableCell>
                        </React.Fragment>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </DialogContent>
      </Dialog>
    </PageContainer>
  );
};

export default Cabinets;