# Application Overview

This document provides an overview of the "Innovative-Centre-Admin" project structure and the core functionality within the main `package/` application, based on analysis of the codebase as of March 28, 2025.

## Project Structure

The repository contains multiple related parts:

1.  **Core Admin Application (`package/`):** A Next.js application serving as an administrative dashboard.
2.  **Book Management System (`book-management-system/`):** Contains documentation for a potentially related system.
3.  **Landing Page (`landingpage/`):** A static HTML/CSS website, possibly the public-facing page.

```mermaid
graph TD
    A[Innovative-Centre-Admin (Root)] --> B(package/);
    A --> C(book-management-system/);
    A --> D(landingpage/);

    B --> B1{Next.js App (Admin Dashboard)};
    B1 --> B2[TypeScript];
    B1 --> B3[Material UI];
    B1 --> B4[Prisma ORM];
    B1 --> B5[API Routes];
    B1 --> B6[Class Mgmt];
    B1 --> B7[Payment Mgmt];
    B1 --> B8[Auth];

    C --> C1[Docs: Implementation Plan];
    C --> C2[Docs: Presentation];

    D --> D1[Static HTML/CSS];
    D --> D2[Docs: Improvements Plan];
    D --> D3[Docs: Redesign Plan];

    subgraph Core Application
        B1
        B2
        B3
        B4
        B5
        B6
        B7
        B8
    end

    subgraph Book Management Docs
        C1
        C2
    end

    subgraph Landing Page
        D1
        D2
        D3
    end
```

## Core Application (`package/`) Details

The `package/` directory contains a Next.js 14/TypeScript admin dashboard for an educational center ("Innovative Centre"). It uses Material UI for the frontend, Prisma with PostgreSQL for the backend/database, and Next.js API routes for communication.

### Core Features:

1.  **Class Management:** Create, view, edit, delete classes (subject, level, teacher, cabinet, schedule, cost, start date, language, stage). Displayed in filterable cards. (`classes/`, `api/classes/`, `ClassDialog`)
2.  **Student Management:** Create, view, edit, delete students (name, phone, join date, payment status). Assign students to multiple classes. Displayed in a paginated, searchable table. (`students/`, `api/students/`, `StudentDialog`)
3.  **Teacher Management:** Create, view, edit, delete teachers (name, contact, status, subjects, qualifications). Displays detailed statistics (student counts, payment rates, classes per level) calculated client-side. (`teachers/`, `api/teachers/`, `api/teachers/[id]/`, `TeacherDialog`)
4.  **Cabinet Management:** Create, view, edit, delete cabinets (name, location, capacity, equipment, status). Displays a visual schedule grid showing cabinet usage by teachers based on class schedules. Prevents deletion if classes are assigned. (`cabinets/`, `api/cabinets/`, `CabinetDialog`)
5.  **Payment Management:** Record, view, edit, delete payments associated with students (amount, date, method, status, description). Uses a custom hook (`usePayments`) with an event bus for state management and API interaction. Displays payments in a table and provides a student-specific history view. (`payments/`, `api/payments/`, `usePayments`)
6.  **Timetable View:** An alternative view of classes, similar to the main Class Management page, but allows adding/removing students directly within the expanded class cards. (`timetable/`)

### Relationships (Database Model Summary):

*   A `Class` has one `Teacher` and one `Cabinet`.
*   A `Class` has many `Student`s (Many-to-Many).
*   A `Student` can be in many `Class`es.
*   A `Student` has many `Payment`s.
*   A `Teacher` has many `Class`es.
*   A `Cabinet` has many `Class`es.

### Data Flow:

React components fetch data via Next.js API routes, which use Prisma to interact with the PostgreSQL database. State changes often trigger API calls, and data is refreshed either directly or via the `usePayments` hook's event bus.

```mermaid
sequenceDiagram
    participant User
    participant FE_Page [Frontend (page.tsx)]
    participant FE_Dialog [Frontend (ClassDialog.tsx)]
    participant API [Backend API (/api/classes)]
    participant Prisma
    participant DB [(PostgreSQL DB)]

    User->>FE_Page: Loads Class Management Page
    FE_Page->>API: GET /api/classes
    API->>Prisma: findMany(include: {teacher, cabinet, students})
    Prisma->>DB: Query Classes & Relations
    DB-->>Prisma: Return Data
    Prisma-->>API: Return Classes
    API-->>FE_Page: Return Classes JSON
    FE_Page->>User: Display Class Cards

    User->>FE_Page: Clicks "Create Class"
    FE_Page->>FE_Dialog: Open Dialog (mode: create)
    FE_Dialog->>API: GET /api/teachers
    FE_Dialog->>API: GET /api/cabinets
    API->>Prisma: Fetch Teachers/Cabinets
    Prisma->>DB: Query Teachers/Cabinets
    DB-->>Prisma: Return Data
    Prisma-->>API: Return Data
    API-->>FE_Dialog: Return Teachers/Cabinets JSON
    FE_Dialog->>User: Display Form with Dropdowns

    User->>FE_Dialog: Fills form & Clicks "Create"
    FE_Dialog->>FE_Page: onSave(formData)
    FE_Page->>API: POST /api/classes (body: formData)
    API->>Prisma: create(data: {..., teacher: connect, cabinet: connect, students: connect})
    Prisma->>DB: Insert New Class & Relations
    DB-->>Prisma: Return New Class Data
    Prisma-->>API: Return New Class
    API-->>FE_Page: Return New Class JSON (201)
    FE_Page->>FE_Page: Add new class to state, Show Success Notification
    FE_Page->>User: Update UI

    %% Similar flows for Edit (PUT) and Delete (DELETE)