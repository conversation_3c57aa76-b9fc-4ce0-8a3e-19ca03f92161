'use client';
import { useState, useMemo, useCallback, useEffect } from 'react';
import { usePayments } from '@/hooks/usePayments';
import { useSearchParams } from 'next/navigation';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Stack,
  IconButton,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Collapse,
  Button,
  Snackbar,
  Alert,
  CircularProgress,
  TextField,
} from '@mui/material';
import PageContainer from '@/app/(DashboardLayout)/components/container/PageContainer';
import type { ClassWithRelations } from '@/types';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import EditIcon from '@mui/icons-material/Edit';
import ClassDialog from './ClassDialog';

interface DialogState {
  open: boolean;
  mode: 'create' | 'edit';
  classData?: ClassWithRelations;
}

interface NotificationState {
  open: boolean;
  message: string;
  severity: 'success' | 'error';
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('uz-UZ', {
    style: 'currency',
    currency: 'UZS',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const Classes = () => {
  const searchParams = useSearchParams();
  const { getStudentPaymentStatus } = usePayments();
  
  const getPaymentStatusColor = (status: string) => {
    return status === 'paid' ? 'success' : 'error';
  };

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'EARLY': return 'info';
      case 'MIDDLE': return 'warning';
      case 'LATE': return 'success';
      default: return 'default';
    }
  };

  const getLanguageColor = (language: string) => {
    switch (language) {
      case 'RUSSIAN': return 'primary';
      case 'UZBEK': return 'secondary';
      case 'MIXED': return 'warning';
      default: return 'default';
    }
  };

  const [selectedLevel, setSelectedLevel] = useState<string>('A1');
  const [selectedLanguage, setSelectedLanguage] = useState<string>('ALL');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedClasses, setExpandedClasses] = useState<string[]>([]);
  const [classes, setClasses] = useState<ClassWithRelations[]>([]);
  const [highlightedClassId, setHighlightedClassId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [notification, setNotification] = useState<NotificationState>({
    open: false,
    message: '',
    severity: 'success',
  });

  const [dialogState, setDialogState] = useState<DialogState>({
    open: false,
    mode: 'create'
  });

  const levels = ['A1', 'A2', 'B1', 'B2', 'IELTS', 'Math', 'IT', 'SAT', 'Individual', 'Speaking', 'Kids'];
  const languages = ['ALL', 'RUSSIAN', 'UZBEK', 'MIXED'];

  const [activeFilter, setActiveFilter] = useState<string | null>(null);

  const quickFilters = [
    {
      label: 'Available Classes',
      value: 'available',
      filter: (c: ClassWithRelations) => c.students.length < c.cabinet.capacity
    },
    {
      label: 'Morning Classes',
      value: 'morning',
      filter: (c: ClassWithRelations) => {
        const schedule = c.schedule as { startTime: string }[];
        return schedule.some(s => s.startTime < '12:00');
      }
    },
    {
      label: 'Afternoon Classes',
      value: 'afternoon',
      filter: (c: ClassWithRelations) => {
        const schedule = c.schedule as { startTime: string }[];
        return schedule.some(s => s.startTime >= '14:00' && s.startTime < '17:00');
      }
    },
    {
      label: 'Evening Classes',
      value: 'evening',
      filter: (c: ClassWithRelations) => {
        const schedule = c.schedule as { startTime: string }[];
        return schedule.some(s => s.startTime >= '16:00');
      }
    },
  ];

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/classes');
      if (!response.ok) throw new Error('Failed to fetch classes');
      const data = await response.json();
      setClasses(data);
    } catch (error) {
      showNotification('Failed to fetch classes', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClasses();
  }, []);

  useEffect(() => {
    const classId = searchParams.get('classId');
    if (classId) {
      const targetClass = classes.find(c => c.id === classId);
      if (targetClass) {
        setSelectedLevel(targetClass.level);
        setExpandedClasses(prev => [...prev, classId]);
        setHighlightedClassId(classId);
        
        setTimeout(() => {
          const element = document.getElementById(`class-${classId}`);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 100);
      }
    }
  }, [searchParams, classes]);

  const handleOpenCreate = () => {
    setDialogState({
      open: true,
      mode: 'create'
    });
  };

  const handleOpenEdit = (classData: ClassWithRelations) => {
    setDialogState({
      open: true,
      mode: 'edit',
      classData
    });
  };

  const handleClose = () => {
    setDialogState({
      open: false,
      mode: 'create'
    });
  };

  const showNotification = (message: string, severity: 'success' | 'error') => {
    setNotification({
      open: true,
      message,
      severity,
    });
  };

  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  const handleSave = async (formData: Partial<ClassWithRelations>) => {
    try {
      const method = dialogState.mode === 'create' ? 'POST' : 'PUT';
      const url = method === 'PUT' ? `/api/classes?id=${formData.id}` : '/api/classes';

      // Transform the data to match API expectations
      const apiData = {
        ...formData,
        teacherId: formData.teacher?.id,
        cabinetId: formData.cabinet?.id,
        students: formData.students?.map(student => student.id),
      };

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(apiData),
      });

      if (!response.ok) throw new Error(`Failed to ${dialogState.mode} class`);
      
      const savedClass = await response.json();
      
      if (dialogState.mode === 'create') {
        setClasses(prev => [...prev, savedClass]);
        showNotification('Class created successfully', 'success');
      } else {
        setClasses(prev =>
          prev.map(c => c.id === savedClass.id ? savedClass : c)
        );
        showNotification('Class updated successfully', 'success');
      }
      handleClose();
    } catch (error) {
      showNotification(
        `Failed to ${dialogState.mode} class`,
        'error'
      );
    }
  };

  const handleDelete = useCallback(async () => {
    if (dialogState.classData) {
      try {
        const response = await fetch(`/api/classes?id=${dialogState.classData.id}`, {
          method: 'DELETE',
        });

        if (!response.ok) throw new Error('Failed to delete class');

        setClasses(prev => prev.filter(c => c.id !== dialogState.classData?.id));
        showNotification('Class deleted successfully', 'success');
        handleClose();
      } catch (error) {
        showNotification('Failed to delete class', 'error');
      }
    }
  }, [dialogState.classData]);

  const getTimeUntilStart = (openingDate?: Date | null) => {
    if (!openingDate) return null;
    
    const start = new Date(openingDate);
    const now = new Date();
    const diffDays = Math.ceil((start.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };

  const getStartDateColor = (daysUntilStart: number | null) => {
    if (daysUntilStart === null) return 'default';
    if (daysUntilStart <= 0) return 'success';
    if (daysUntilStart <= 3) return 'warning';
    if (daysUntilStart <= 7) return 'info';
    return 'default';
  };

  const getCabinetFillColor = (studentsCount: number, capacity: number) => {
    const remainingSeats = capacity - studentsCount;
    if (remainingSeats <= 0) return 'error';
    if (remainingSeats <= 2) return 'error';
    if (remainingSeats <= 5) return 'warning';
    return 'success';
  };

  const filteredClasses = useMemo(() => {
    return classes.filter(c => {
      // Level filter
      if (c.level !== selectedLevel) return false;
      
      // Language filter
      if (selectedLanguage !== 'ALL' && c.language !== selectedLanguage) return false;
      
      // Quick filter
      if (activeFilter) {
        const filter = quickFilters.find(f => f.value === activeFilter);
        if (filter && !filter.filter(c)) return false;
      }
      
      // Search term
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesTeacher = c.teacher.name.toLowerCase().includes(searchLower);
        const matchesClass = c.name.toLowerCase().includes(searchLower);
        if (!matchesTeacher && !matchesClass) return false;
      }
      
      return true;
    });
  }, [selectedLevel, selectedLanguage, searchTerm, classes, activeFilter]);

  const sortClassesByTime = useCallback((classes: ClassWithRelations[]) => {
    return [...classes].sort((a, b) => {
      const scheduleA = a.schedule as { day: string; startTime: string; endTime: string; }[];
      const scheduleB = b.schedule as { day: string; startTime: string; endTime: string; }[];
      
      const timeA = scheduleA[0]?.startTime || '00:00';
      const timeB = scheduleB[0]?.startTime || '00:00';
      
      return timeA.localeCompare(timeB);
    });
  }, []);

  const mwfClasses = useMemo(() => {
    const filtered = filteredClasses.filter(c => {
      const schedule = c.schedule as { day: string; startTime: string; endTime: string; }[];
      return schedule.some(s => ['Monday', 'Wednesday', 'Friday'].includes(s.day));
    });
    return sortClassesByTime(filtered);
  }, [filteredClasses, sortClassesByTime]);

  const ttsClasses = useMemo(() => {
    const filtered = filteredClasses.filter(c => {
      const schedule = c.schedule as { day: string; startTime: string; endTime: string; }[];
      return schedule.some(s => ['Tuesday', 'Thursday', 'Saturday'].includes(s.day));
    });
    return sortClassesByTime(filtered);
  }, [filteredClasses, sortClassesByTime]);

  const toggleClassExpansion = (classId: string) => {
    setExpandedClasses(prev => 
      prev.includes(classId) 
        ? prev.filter(id => id !== classId)
        : [...prev, classId]
    );
  };

  const renderClassCard = (classInfo: ClassWithRelations) => {
    const isExpanded = expandedClasses.includes(classInfo.id);
    const schedule = classInfo.schedule as { day: string; startTime: string; endTime: string; }[];
    const scheduleStr = schedule.length > 0
      ? `${schedule[0].startTime} - ${schedule[0].endTime}`
      : 'No time set';

    const daysUntilStart = getTimeUntilStart(classInfo.openingDate);
    const startDateColor = getStartDateColor(daysUntilStart);
    const cabinetFillColor = getCabinetFillColor(classInfo.students.length, classInfo.cabinet.capacity);

    const startDateText = daysUntilStart !== null
      ? daysUntilStart <= 0
        ? 'Started'
        : `Starts in ${daysUntilStart} days`
      : 'Start date not set';

    return (
      <Card
        id={`class-${classInfo.id}`}
        key={classInfo.id}
        sx={{
          mb: 2,
          minHeight: '150px',
          transition: 'all 0.3s ease-in-out',
          ...(highlightedClassId === classInfo.id && {
            border: '2px solid',
            borderColor: 'primary.main',
            boxShadow: (theme) => theme.shadows[8],
            transform: 'scale(1.02)',
            bgcolor: 'primary.50',
            '& .MuiTypography-root': {
              color: 'primary.dark'
            }
          })
        }}
      >
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography
                variant="h5"
                component="div"
                sx={{ fontWeight: 500, mb: 1 }}
              >
                {classInfo.name}
              </Typography>
              <Typography
                variant="subtitle1"
                sx={{
                  color: 'text.secondary',
                  mb: 2,
                  '& .separator': {
                    mx: 1,
                    color: 'text.disabled'
                  }
                }}
              >
                Level {classInfo.level} <span className="separator">|</span> {formatCurrency(classInfo.courseAmount)}
              </Typography>
              <Typography color="textSecondary" sx={{ fontSize: '1rem', mb: 0.5 }}>
                Teacher: {classInfo.teacher.name}
              </Typography>
              <Typography color="textSecondary" sx={{ fontSize: '1rem', mb: 0.5 }}>
                {scheduleStr}
              </Typography>
              <Typography color="textSecondary" sx={{ fontSize: '1rem' }}>
                Start date: {classInfo.openingDate
                  ? new Date(classInfo.openingDate).toLocaleDateString('en-GB', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric'
                    })
                  : 'Not set'
                }
              </Typography>
            </Box>
            <Stack direction="row" spacing={1}>
              <IconButton size="small" onClick={() => toggleClassExpansion(classInfo.id)}>
                {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
              <IconButton size="small" onClick={() => handleOpenEdit(classInfo)}>
                <EditIcon />
              </IconButton>
            </Stack>
          </Box>

          <Stack direction="row" spacing={1} mt={1}>
            <Chip
              label={classInfo.cabinet.name}
              size="small"
              color="primary"
              sx={{ fontSize: '1rem' }}
            />
            <Chip
              label={`${classInfo.students.length}/${classInfo.cabinet.capacity} students`}
              size="small"
              color={cabinetFillColor}
              sx={{ fontSize: '1rem' }}
            />
            <Chip
              label={startDateText}
              size="small"
              color={startDateColor}
              sx={{ fontSize: '1rem' }}
            />
            <Chip
              label={classInfo.language || 'RUSSIAN'}
              size="small"
              color={getLanguageColor(classInfo.language || 'RUSSIAN')}
              sx={{ fontSize: '1rem' }}
            />
          </Stack>

          <Collapse in={isExpanded}>
            <Box mt={2}>
              <Typography variant="h6" gutterBottom>
                Students
              </Typography>
              <Grid container spacing={2}>
                {classInfo.students.map((student) => (
                  <Grid item xs={12} key={student.id}>
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                        py: 1
                      }}
                    >
                      <Box sx={{ display: 'flex', gap: 2, fontSize: '1.1rem' }}>
                        <Typography>{student.name}</Typography>
                        <Typography color="textSecondary">{student.phone}</Typography>
                      </Box>
                      <Chip
                        label={getStudentPaymentStatus(student.id)}
                        size="small"
                        sx={{ fontSize: '0.9rem' }}
                        color={
                          getPaymentStatusColor(getStudentPaymentStatus(student.id))
                        }
                      />
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Collapse>
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <PageContainer title="Classes" description="Manage classes by level">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </PageContainer>
    );
  }

  return (
    <PageContainer title="Classes" description="Manage classes by level">
      <Box sx={{ mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h3">Classes</Typography>
          <Button variant="contained" color="primary" onClick={handleOpenCreate}>
            Add New Class
          </Button>
        </Box>
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth>
              <InputLabel>Select Level</InputLabel>
              <Select
                value={selectedLevel}
                label="Select Level"
                onChange={(e) => setSelectedLevel(e.target.value)}
              >
                {levels.map((level) => (
                  <MenuItem key={level} value={level}>
                    {level}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth>
              <InputLabel>Language</InputLabel>
              <Select
                value={selectedLanguage}
                label="Language"
                onChange={(e) => setSelectedLanguage(e.target.value)}
              >
                {languages.map((lang) => (
                  <MenuItem key={lang} value={lang}>
                    {lang === 'ALL' ? 'All Languages' : lang}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Search by teacher or class name"
              value={searchTerm}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
            />
          </Grid>
          <Grid item xs={12}>
            <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
              {quickFilters.map((filter) => (
                <Button
                  key={filter.value}
                  variant={activeFilter === filter.value ? 'contained' : 'outlined'}
                  size="small"
                  onClick={() => setActiveFilter(activeFilter === filter.value ? null : filter.value)}
                  sx={{ borderRadius: '20px' }}
                >
                  {filter.label}
                </Button>
              ))}
            </Stack>
          </Grid>
        </Grid>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, mb: { xs: 2, md: 0 } }}>
            <Typography variant="h5" gutterBottom color="primary">
              Monday/Wednesday/Friday
            </Typography>
            {mwfClasses.map(renderClassCard)}
            {mwfClasses.length === 0 && (
              <Typography color="textSecondary" align="center" sx={{ py: 4 }}>
                No classes scheduled
              </Typography>
            )}
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h5" gutterBottom color="primary">
              Tuesday/Thursday/Saturday
            </Typography>
            {ttsClasses.map(renderClassCard)}
            {ttsClasses.length === 0 && (
              <Typography color="textSecondary" align="center" sx={{ py: 4 }}>
                No classes scheduled
              </Typography>
            )}
          </Paper>
        </Grid>
      </Grid>

      <ClassDialog
        open={dialogState.open}
        onClose={handleClose}
        onSave={handleSave}
        onDelete={dialogState.mode === 'edit' ? handleDelete : undefined}
        classData={dialogState.classData}
        mode={dialogState.mode}
      />

      <Snackbar
        open={notification.open}
        autoHideDuration={4000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </PageContainer>
  );
};

export default Classes;