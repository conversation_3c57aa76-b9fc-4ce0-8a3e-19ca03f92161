'use client'
import { Grid, Box, Card, Typography, Stack, CircularProgress, Alert } from '@mui/material';
import PageContainer from '@/app/(DashboardLayout)/components/container/PageContainer';
import { Suspense, useEffect, useState } from 'react';
import { usePayments } from '@/hooks/usePayments';
import { StudentWithClasses, TeacherWithClasses, ClassWithRelations, CabinetWithClasses, PaymentWithStudent } from '@/types';

// Loading Component
const LoadingCard = () => (
  <Card sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '150px' }}>
    <CircularProgress />
  </Card>
);

// Error Component
const ErrorCard = ({ message }: { message: string }) => (
  <Card sx={{ p: 3 }}>
    <Alert severity="error">{message}</Alert>
  </Card>
);

// Stats Card Component
const StatsCard = ({ title, value, color }: { title: string; value: string | number; color: string }) => (
  <Card sx={{ p: 3 }}>
    <Stack spacing={1}>
      <Typography variant="subtitle2" color="textSecondary">
        {title}
      </Typography>
      <Typography variant="h4" color={color}>
        {value}
      </Typography>
    </Stack>
  </Card>
);

// Stats Overview Component
const StatsOverview = () => {
  const [students, setStudents] = useState<StudentWithClasses[]>([]);
  const { getStudentPaymentStatus } = usePayments();
  const [teachers, setTeachers] = useState<TeacherWithClasses[]>([]);
  const [classes, setClasses] = useState<ClassWithRelations[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [studentsRes, teachersRes, classesRes] = await Promise.all([
          fetch('/api/students'),
          fetch('/api/teachers'),
          fetch('/api/classes')
        ]);

        if (!studentsRes.ok || !teachersRes.ok || !classesRes.ok) {
          throw new Error('Failed to fetch data');
        }

        const [studentsData, teachersData, classesData] = await Promise.all([
          studentsRes.json(),
          teachersRes.json(),
          classesRes.json()
        ]);

        setStudents(studentsData);
        setTeachers(teachersData);
        setClasses(classesData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (error) {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <ErrorCard message={error} />
        </Grid>
      </Grid>
    );
  }

  if (loading) {
    return (
      <Grid container spacing={3}>
        {[1, 2, 3].map((i) => (
          <Grid item xs={12} sm={4} key={i}>
            <LoadingCard />
          </Grid>
        ))}
      </Grid>
    );
  }

  const activeStudents = students.filter(s => getStudentPaymentStatus(s.id) === 'paid').length;
  const activeTeachers = teachers.filter(t => t.status === 'active').length;
  const totalClasses = classes.length;

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={4}>
        <StatsCard 
          title="Active Students" 
          value={activeStudents}
          color="primary.main"
        />
      </Grid>
      <Grid item xs={12} sm={4}>
        <StatsCard 
          title="Active Teachers" 
          value={activeTeachers}
          color="success.main"
        />
      </Grid>
      <Grid item xs={12} sm={4}>
        <StatsCard 
          title="Total Classes" 
          value={totalClasses}
          color="warning.main"
        />
      </Grid>
    </Grid>
  );
};

// Class Levels Component
const ClassLevels = () => {
  const [classes, setClasses] = useState<ClassWithRelations[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchClasses = async () => {
      try {
        const response = await fetch('/api/classes');
        if (!response.ok) throw new Error('Failed to fetch classes');
        const data = await response.json();
        setClasses(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchClasses();
  }, []);

  if (error) return <ErrorCard message={error} />;
  if (loading) return <LoadingCard />;

  const levels = classes.reduce((acc: Record<string, number>, cls) => {
    acc[cls.level] = (acc[cls.level] || 0) + 1;
    return acc;
  }, {});

  return (
    <Card sx={{ p: 3, minHeight: '240px', display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" mb={2}>Class Distribution</Typography>
      <Stack spacing={2} sx={{ flex: 1 }}>
        {Object.keys(levels).length > 0 ? (
          Object.entries(levels).map(([level, count]) => (
            <Box
              key={level}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              sx={{ py: 1 }}
            >
              <Typography variant="subtitle1">{level}</Typography>
              <Typography variant="h6" color="primary">{count} classes</Typography>
            </Box>
          ))
        ) : (
          <Box display="flex" justifyContent="center" alignItems="center" sx={{ flex: 1 }}>
            <Typography variant="subtitle1" color="textSecondary">
              No classes available
            </Typography>
          </Box>
        )}
      </Stack>
    </Card>
  );
};

// Recent Payments Component
const RecentPayments = () => {
  const [payments, setPayments] = useState<PaymentWithStudent[]>([]);
  const { getStudentPaymentStatus } = usePayments();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPayments = async () => {
      try {
        const response = await fetch('/api/payments');
        if (!response.ok) throw new Error('Failed to fetch payments');
        const data = await response.json();
        setPayments(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchPayments();
  }, []);

  if (error) return <ErrorCard message={error} />;
  if (loading) return <LoadingCard />;

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('uz-UZ', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }) + ' UZS';
  };

  return (
    <Card sx={{ p: 3, minHeight: '240px', display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" mb={2}>Recent Payments</Typography>
      <Stack spacing={2} sx={{ flex: 1 }}>
        {payments.length > 0 ? (
          payments.slice(0, 5).map((payment) => (
            <Box
              key={payment.id}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              sx={{ py: 1 }}
            >
              <Stack>
                <Typography variant="subtitle2">{payment.student.name}</Typography>
                <Typography variant="caption" color="textSecondary">
                  {new Date(payment.date).toLocaleDateString()}
                </Typography>
              </Stack>
              <Typography
                variant="subtitle1"
                color={getStudentPaymentStatus(payment.student.id) === 'paid' ? 'success.main' : 'error.main'}
              >
                {formatCurrency(payment.amount)}
              </Typography>
            </Box>
          ))
        ) : (
          <Box display="flex" justifyContent="center" alignItems="center" sx={{ flex: 1 }}>
            <Typography variant="subtitle1" color="textSecondary">
              No recent payments
            </Typography>
          </Box>
        )}
      </Stack>
    </Card>
  );
};

// Cabinet Status Component
const CabinetStatus = () => {
  const [cabinets, setCabinets] = useState<CabinetWithClasses[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCabinets = async () => {
      try {
        const response = await fetch('/api/cabinets');
        if (!response.ok) throw new Error('Failed to fetch cabinets');
        const data = await response.json();
        setCabinets(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchCabinets();
  }, []);

  if (error) return <ErrorCard message={error} />;
  if (loading) return <LoadingCard />;

  return (
    <Card sx={{ p: 3, minHeight: '240px', display: 'flex', flexDirection: 'column' }}>
      <Typography variant="h6" mb={2}>Cabinet Status</Typography>
      <Stack
        spacing={2}
        sx={{
          flex: 1,
          overflow: 'auto',
          maxHeight: 'calc(240px - 64px)', // Account for header height
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,0.2)',
            borderRadius: '3px',
          }
        }}
      >
        {cabinets.length > 0 ? (
          cabinets.map((cabinet) => (
            <Box
              key={cabinet.id}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              sx={{ py: 1 }}
            >
              <Stack>
                <Typography variant="subtitle2">{cabinet.name}</Typography>
                <Typography variant="caption" color="textSecondary">
                  Capacity: {cabinet.capacity}
                </Typography>
              </Stack>
              <Typography
                variant="subtitle2"
                sx={{
                  color: cabinet.status === 'available'
                    ? 'success.main'
                    : cabinet.status === 'occupied'
                    ? 'warning.main'
                    : 'error.main'
                }}
              >
                {cabinet.status.charAt(0).toUpperCase() + cabinet.status.slice(1)}
              </Typography>
            </Box>
          ))
        ) : (
          <Box display="flex" justifyContent="center" alignItems="center" sx={{ flex: 1 }}>
            <Typography variant="subtitle1" color="textSecondary">
              No cabinets available
            </Typography>
          </Box>
        )}
      </Stack>
    </Card>
  );
};

const Dashboard = () => {
  return (
    <PageContainer title="Dashboard" description="Centre Overview">
      <Box>
        <Grid container spacing={3}>
          {/* Stats Overview */}
          <Grid item xs={12}>
            <Suspense fallback={<LoadingCard />}>
              <StatsOverview />
            </Suspense>
          </Grid>

          {/* Class Levels */}
          <Grid item xs={12} md={6}>
            <Suspense fallback={<LoadingCard />}>
              <ClassLevels />
            </Suspense>
          </Grid>

          {/* Recent Payments */}
          <Grid item xs={12} md={6}>
            <Suspense fallback={<LoadingCard />}>
              <RecentPayments />
            </Suspense>
          </Grid>

          {/* Cabinet Status */}
          <Grid item xs={12}>
            <Suspense fallback={<LoadingCard />}>
              <CabinetStatus />
            </Suspense>
          </Grid>
        </Grid>
      </Box>
    </PageContainer>
  )
}

export default Dashboard;
