import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { NextRequest } from 'next/server';

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'ADMIN' | 'RECEPTION' | 'TEACHER' | 'STUDENT';
  permissions: string[];
}

export interface AuthResult {
  success: boolean;
  user?: User;
  token?: string;
  refreshToken?: string;
  error?: string;
}

// Secure admin configuration from environment
const ADMIN_USERS = [
  {
    id: 'admin-parviz',
    email: process.env.ADMIN_PARVIZ_EMAIL || '<EMAIL>',
    passwordHash: process.env.ADMIN_PARVIZ_PASSWORD_HASH || '',
    name: 'Parviz',
    role: 'ADMIN' as const,
    permissions: ['*'] // Full access
  },
  {
    id: 'admin-reception',
    email: process.env.ADMIN_RECEPTION_EMAIL || '<EMAIL>',
    passwordHash: process.env.ADMIN_RECEPTION_PASSWORD_HASH || '',
    name: 'Reception',
    role: 'RECEPTION' as const,
    permissions: ['students:read', 'students:write', 'classes:read', 'payments:read']
  },
  {
    id: 'admin-mukhammadkhon',
    email: process.env.ADMIN_MUKHAMMADKHON_EMAIL || '<EMAIL>',
    passwordHash: process.env.ADMIN_MUKHAMMADKHON_PASSWORD_HASH || '',
    name: 'Mukhammadkhon',
    role: 'ADMIN' as const,
    permissions: ['*'] // Full access
  }
];

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-change-immediately';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

// Hash password utility
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

// Verify password utility
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return await bcrypt.compare(password, hash);
}

// Generate JWT token
export function generateToken(user: User): string {
  return jwt.sign(
    {
      userId: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions
    },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES_IN }
  );
}

// Generate refresh token
export function generateRefreshToken(user: User): string {
  return jwt.sign(
    { userId: user.id },
    JWT_SECRET,
    { expiresIn: JWT_REFRESH_EXPIRES_IN }
  );
}

// Verify JWT token
export function verifyToken(token: string): User | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    return {
      id: decoded.userId,
      email: decoded.email,
      name: decoded.name || '',
      role: decoded.role,
      permissions: decoded.permissions || []
    };
  } catch (error) {
    return null;
  }
}

// Authenticate user with email and password
export async function authenticate(email: string, password: string): Promise<AuthResult> {
  try {
    // Find user by email
    const adminUser = ADMIN_USERS.find(user => user.email.toLowerCase() === email.toLowerCase());
    
    if (!adminUser) {
      return { success: false, error: 'Invalid credentials' };
    }

    // Verify password
    const isValidPassword = await verifyPassword(password, adminUser.passwordHash);
    
    if (!isValidPassword) {
      return { success: false, error: 'Invalid credentials' };
    }

    // Create user object
    const user: User = {
      id: adminUser.id,
      email: adminUser.email,
      name: adminUser.name,
      role: adminUser.role,
      permissions: adminUser.permissions
    };

    // Generate tokens
    const token = generateToken(user);
    const refreshToken = generateRefreshToken(user);

    return {
      success: true,
      user,
      token,
      refreshToken
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return { success: false, error: 'Authentication failed' };
  }
}

// Extract token from request
export function extractTokenFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Also check cookies as fallback
  const tokenCookie = request.cookies.get('auth-token');
  return tokenCookie?.value || null;
}

// Middleware to verify authentication
export function requireAuth(request: NextRequest): User | null {
  const token = extractTokenFromRequest(request);
  
  if (!token) {
    return null;
  }
  
  return verifyToken(token);
}

// Check if user has specific permission
export function hasPermission(user: User, permission: string): boolean {
  if (user.permissions.includes('*')) {
    return true; // Admin has all permissions
  }
  
  return user.permissions.includes(permission);
}

// Role-based access control
export function requireRole(user: User, allowedRoles: string[]): boolean {
  return allowedRoles.includes(user.role);
}
