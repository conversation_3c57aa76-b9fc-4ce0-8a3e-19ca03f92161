'use client';
import {
  Card,
  Grid,
  Typo<PERSON>,
  Box,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Paper,
  CircularProgress,
} from '@mui/material';
import { IconEdit, IconTrash, IconPlus } from '@tabler/icons-react';
import PageContainer from '@/app/(DashboardLayout)/components/container/PageContainer';
import DashboardCard from '@/app/(DashboardLayout)/components/shared/DashboardCard';
import TeacherDialog from './TeacherDialog';
import { useState, useEffect } from 'react';
import { usePayments } from '@/hooks/usePayments';
import { Teacher, TeacherWithStats } from '@/types/teacher';

// API functions
const fetchTeachers = async (): Promise<Teacher[]> => {
  const response = await fetch('/api/teachers');
  if (!response.ok) throw new Error('Failed to fetch teachers');
  return response.json();
};

const createTeacher = async (teacherData: Omit<Teacher, 'id'>) => {
  const response = await fetch('/api/teachers', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(teacherData),
  });
  if (!response.ok) throw new Error('Failed to create teacher');
  return response.json();
};

const updateTeacher = async (id: string, teacherData: Partial<Teacher>) => {
  const response = await fetch(`/api/teachers/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(teacherData),
  });
  if (!response.ok) throw new Error('Failed to update teacher');
  return response.json();
};

const deleteTeacher = async (id: string) => {
  const response = await fetch(`/api/teachers/${id}`, {
    method: 'DELETE',
  });
  if (!response.ok) throw new Error('Failed to delete teacher');
  return response.json();
};

const TeachersPage = () => {
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | undefined>();
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [deleteTeacherId, setDeleteTeacherId] = useState<string | null>(null);

  useEffect(() => {
    loadTeachers();
  }, []);

  const loadTeachers = async () => {
    try {
      setLoading(true);
      const data = await fetchTeachers();
      setTeachers(data);
      setError(null);
    } catch (err) {
      setError('Failed to load teachers');
      console.error('Error loading teachers:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddTeacher = () => {
    setSelectedTeacher(undefined);
    setOpenDialog(true);
  };

  const handleEditTeacher = (teacher: Teacher) => {
    setSelectedTeacher(teacher);
    setOpenDialog(true);
  };

  const handleDeleteClick = (teacher: Teacher) => {
    setDeleteTeacherId(teacher.id);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (deleteTeacherId) {
      try {
        await deleteTeacher(deleteTeacherId);
        await loadTeachers(); // Reload the list after deletion
        setDeleteConfirmOpen(false);
        setDeleteTeacherId(null);
      } catch (err) {
        setError('Failed to delete teacher');
        console.error('Error deleting teacher:', err);
      }
    }
  };

  const handleSaveTeacher = async (teacherData: Partial<Teacher>) => {
    try {
      if (selectedTeacher) {
        // Edit existing teacher
        await updateTeacher(selectedTeacher.id, teacherData);
      } else {
        // Add new teacher
        await createTeacher(teacherData as Omit<Teacher, 'id'>);
      }
      await loadTeachers(); // Reload the list after save
      setOpenDialog(false);
    } catch (err) {
      setError('Failed to save teacher');
      console.error('Error saving teacher:', err);
    }
  };

  const { getStudentPaymentStatus } = usePayments();

  // Calculate statistics for each teacher
  const teacherStats: TeacherWithStats[] = teachers.map(teacher => {
    const teacherClasses = teacher.classes || [];
    const uniqueStudents = new Set(teacherClasses.flatMap(cls => cls.students.map(student => student.id)));
    const teacherStudents = Array.from(uniqueStudents).map(id =>
      teacherClasses.flatMap(cls => cls.students).find(student => student.id === id)
    ).filter(Boolean);

    const paidStudents = teacherStudents.filter(student =>
      student && getStudentPaymentStatus(student.id) === 'paid'
    );
    const unpaidStudents = teacherStudents.filter(student =>
      student && getStudentPaymentStatus(student.id) !== 'paid'
    );
    const newStudents = teacherStudents.filter(student => {
      const joinDate = new Date(student?.joinDate || '');
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
      return joinDate >= oneMonthAgo;
    });

    // Group classes by level
    const levelGroups = teacherClasses.reduce((acc: Record<string, number>, cls) => {
      acc[cls.level] = (acc[cls.level] || 0) + 1;
      return acc;
    }, {});

    return {
      ...teacher,
      classCount: teacherClasses.length,
      totalStudents: teacherStudents.length,
      paidStudents: paidStudents.length,
      unpaidStudents: unpaidStudents.length,
      newStudents: newStudents.length,
      levelGroups,
    };
  });

  const renderContent = () => {
    if (loading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      );
    }

    if (teachers.length === 0) {
      return (
        <Box textAlign="center" py={4}>
          <Typography variant="h6" color="textSecondary" gutterBottom>
            No teachers found
          </Typography>
          <Typography color="textSecondary" paragraph>
            Add a teacher to get started
          </Typography>
        </Box>
      );
    }

    return (
      <Grid container spacing={3}>
        {/* Overall Statistics */}
        <Grid item xs={12}>
          <DashboardCard title="Overall Statistics">
            <Grid container spacing={2}>
              <Grid item xs={12} sm={3}>
                <Card sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h3">{teachers.length}</Typography>
                  <Typography variant="subtitle1">Total Teachers</Typography>
                </Card>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Card sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h3">
                    {teachers.reduce((total, teacher) => total + (teacher.classes?.length || 0), 0)}
                  </Typography>
                  <Typography variant="subtitle1">Total Classes</Typography>
                </Card>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Card sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h3">
                    {(teachers.reduce((total, teacher) => total + (teacher.classes?.length || 0), 0) / (teachers.length || 1)).toFixed(1)}
                  </Typography>
                  <Typography variant="subtitle1">Avg Classes per Teacher</Typography>
                </Card>
              </Grid>
              <Grid item xs={12} sm={3}>
                <Card sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h3">
                    {(teacherStats.reduce((total, teacher) => total + teacher.totalStudents, 0) / (teachers.length || 1)).toFixed(1)}
                  </Typography>
                  <Typography variant="subtitle1">Avg Students per Teacher</Typography>
                </Card>
              </Grid>
            </Grid>
          </DashboardCard>
        </Grid>

        {/* Teacher Details Table */}
        <Grid item xs={12}>
          <DashboardCard title="Teacher Details">
            <Box sx={{ overflow: 'auto' }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell><strong>Teacher</strong></TableCell>
                    <TableCell><strong>Classes</strong></TableCell>
                    <TableCell><strong>Students</strong></TableCell>
                    <TableCell><strong>New Students</strong></TableCell>
                    <TableCell><strong>Paid/Unpaid</strong></TableCell>
                    <TableCell><strong>Groups by Level</strong></TableCell>
                    <TableCell><strong>Actions</strong></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {teacherStats.map((teacher) => (
                    <TableRow key={teacher.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle1" fontWeight={600}>
                            {teacher.name}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {teacher.email}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{teacher.classCount}</TableCell>
                      <TableCell>{teacher.totalStudents}</TableCell>
                      <TableCell>{teacher.newStudents}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Chip 
                            label={`${teacher.paidStudents} Оплатил`}
                            color="success"
                            size="small"
                          />
                          <Chip
                            label={`${teacher.unpaidStudents} Не оплатил`}
                            color="error"
                            size="small"
                          />
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                          {Object.entries(teacher.levelGroups).map(([level, count]) => (
                            <Chip
                              key={level}
                              label={`${level}: ${count}`}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <IconButton
                            color="primary"
                            size="small"
                            onClick={() => handleEditTeacher(teacher)}
                          >
                            <IconEdit size={18} />
                          </IconButton>
                          <IconButton
                            color="error"
                            size="small"
                            onClick={() => handleDeleteClick(teacher)}
                          >
                            <IconTrash size={18} />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          </DashboardCard>
        </Grid>

        {/* Individual Teacher Cards */}
        {teacherStats.map((teacher) => (
          <Grid item xs={12} md={6} key={teacher.id}>
            <DashboardCard title={teacher.name}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="textSecondary">
                      Contact: {teacher.email} | {teacher.phone}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Joined: {new Date(teacher.joinDate).toLocaleDateString()}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                    {teacher.qualifications.map((qual, index) => (
                      <Chip key={index} label={qual} size="small" />
                    ))}
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6">{teacher.classCount}</Typography>
                    <Typography variant="body2">Classes</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6">{teacher.totalStudents}</Typography>
                    <Typography variant="body2">Students</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6">{teacher.newStudents}</Typography>
                    <Typography variant="body2">New</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6">
                      {((teacher.paidStudents / (teacher.totalStudents || 1)) * 100).toFixed(0)}%
                    </Typography>
                    <Typography variant="body2">Payment Rate</Typography>
                  </Paper>
                </Grid>
              </Grid>
            </DashboardCard>
          </Grid>
        ))}
      </Grid>
    );
  };

  return (
    <PageContainer title="Teachers" description="Teacher Information and Statistics">
      {/* Add Button */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<IconPlus />}
          onClick={handleAddTeacher}
        >
          Add Teacher
        </Button>
      </Box>

      {renderContent()}

      {/* Add/Edit Teacher Dialog */}
      <TeacherDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        onSave={handleSaveTeacher}
        teacher={selectedTeacher}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Delete Teacher</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this teacher? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default TeachersPage;