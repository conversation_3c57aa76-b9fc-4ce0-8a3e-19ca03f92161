import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  OutlinedInput,
  Typography,
  InputAdornment,
} from '@mui/material';
import { IconSearch } from '@tabler/icons-react';
import type { StudentWithClasses } from '@/types';

interface ClassOption {
  id: string;
  name: string;
  subject: string;
  level: string;
  stage: 'EARLY' | 'MIDDLE' | 'LATE';
  language: 'RUSSIAN' | 'UZBEK' | 'MIXED';
  schedule: any[];
  cabinet: {
    id: string;
    name: string;
    location: string;
  };
  teacher: {
    id: string;
    name: string;
  };
}

interface StudentFormData {
  name: string;
  phone: string;
  joinDate?: Date;
  paymentStatus?: string;
  selectedClassIds?: string[];
}

interface StudentDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (student: StudentFormData) => void;
  student?: StudentWithClasses;
}

const StudentDialog: React.FC<StudentDialogProps> = ({
  open,
  onClose,
  onSave,
  student,
}) => {
  const [formData, setFormData] = useState<StudentFormData>({
    name: '',
    phone: '',
    selectedClassIds: [],
  });

  const [classes, setClasses] = useState<ClassOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [classSearchQuery, setClassSearchQuery] = useState('');
  const [classesDropdownOpen, setClassesDropdownOpen] = useState(false);

  useEffect(() => {
    fetchClasses();
  }, []);

  useEffect(() => {
    if (student) {
      setFormData({
        name: student.name,
        phone: student.phone,
        joinDate: student.joinDate,
        paymentStatus: student.paymentStatus,
        selectedClassIds: student.classes.map(c => c.id),
      });
    } else {
      setFormData({
        name: '',
        phone: '',
        selectedClassIds: [],
      });
    }
  }, [student]);

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/classes');
      if (!response.ok) throw new Error('Failed to fetch classes');
      const data = await response.json();
      setClasses(data);
    } catch (error) {
      console.error('Failed to fetch classes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleClassChange = (event: any) => {
    const selectedClassIds = event.target.value as string[];
    setFormData({
      ...formData,
      selectedClassIds,
    });
    setClassesDropdownOpen(false); // Close the dropdown after selection
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      ...formData,
      joinDate: formData.joinDate || new Date(),
      paymentStatus: formData.paymentStatus || 'unpaid',
    });
  };

  const getScheduleDays = (schedule: any[]) => {
    const days = schedule.map(s => s.day);
    const dayGroups = {
      'M/W/F': ['Monday', 'Wednesday', 'Friday'],
      'T/T/S': ['Tuesday', 'Thursday', 'Saturday']
    };
    
    for (const [group, groupDays] of Object.entries(dayGroups)) {
      if (groupDays.every(day => days.includes(day))) {
        return group;
      }
    }
    return days.join('/');
  };

  const formatTime = (schedule: any) => {
    if (schedule.startTime && schedule.endTime) {
      return `${schedule.startTime} - ${schedule.endTime}`;
    }
    return schedule.time || '';
  };

  const getClassLabel = (classId: string) => {
    const cls = classes.find(c => c.id === classId);
    if (!cls) return classId;

    const schedule = cls.schedule[0] || {};
    const time = formatTime(schedule);
    const days = getScheduleDays(cls.schedule);
    
    return `${cls.name} (${time}, ${cls.cabinet.name.trim()}, ${days})`;
  };

  const filteredClasses = classes.filter(cls => {
    const searchLower = classSearchQuery.toLowerCase();
    return (
      cls.name.toLowerCase().includes(searchLower) ||
      cls.subject.toLowerCase().includes(searchLower) ||
      cls.level.toLowerCase().includes(searchLower) ||
      cls.teacher.name.toLowerCase().includes(searchLower) ||
      cls.cabinet.name.toLowerCase().includes(searchLower)
    );
  });

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <form onSubmit={handleSubmit}>
        <DialogTitle>
          {student ? 'Edit Student' : 'Add New Student'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              fullWidth
            />
            <TextField
              label="Phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              required
              fullWidth
            />
            <TextField
              placeholder="Search classes..."
              value={classSearchQuery}
              onChange={(e) => setClassSearchQuery(e.target.value)}
              fullWidth
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <IconSearch size={20} />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 1 }}
            />
            <FormControl fullWidth>
              <InputLabel>Classes</InputLabel>
              <Select
                multiple
                open={classesDropdownOpen}
                onOpen={() => setClassesDropdownOpen(true)}
                onClose={() => setClassesDropdownOpen(false)}
                value={formData.selectedClassIds || []}
                onChange={handleClassChange}
                input={<OutlinedInput label="Classes" />}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip
                        key={value}
                        label={getClassLabel(value)}
                        size="small"
                      />
                    ))}
                  </Box>
                )}
              >
                {filteredClasses.map((cls) => {
                  const schedule = cls.schedule[0] || {};
                  const time = formatTime(schedule);
                  const days = getScheduleDays(cls.schedule);

                  return (
                    <MenuItem key={cls.id} value={cls.id}>
                      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Typography variant="body1">
                          {cls.name} ({cls.level}) - {cls.teacher.name}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {time}, {cls.cabinet.name.trim()}, {days}
                        </Typography>
                      </Box>
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Payment Status</InputLabel>
              <Select
                name="paymentStatus"
                value={formData.paymentStatus || 'unpaid'}
                onChange={(e) => setFormData({
                  ...formData,
                  paymentStatus: e.target.value
                })}
                label="Payment Status"
              >
                <MenuItem value="paid">Paid</MenuItem>
                <MenuItem value="unpaid">Unpaid</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="contained" color="primary">
            {student ? 'Save Changes' : 'Add Student'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default StudentDialog;