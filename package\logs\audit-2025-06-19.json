[{"userId": null, "action": "LOGIN_FAILED", "entity": "AUTH", "entityId": "<EMAIL>", "oldData": null, "newData": null, "ipAddress": "::1", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "critical", "details": {"email": "<EMAIL>"}, "createdAt": "2025-06-19T05:46:25.398Z"}, {"userId": null, "action": "SUSPICIOUS_ACTIVITY", "entity": "SECURITY", "entityId": "suspicious-activity", "oldData": null, "newData": null, "ipAddress": "::1", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "critical", "details": {"description": "Login API error", "error": "[\n  {\n    \"validation\": \"email\",\n    \"code\": \"invalid_string\",\n    \"message\": \"Invalid email format\",\n    \"path\": [\n      \"email\"\n    ]\n  }\n]"}, "createdAt": "2025-06-19T05:46:40.949Z"}, {"userId": null, "action": "LOGIN_FAILED", "entity": "AUTH", "entityId": "<EMAIL>", "oldData": null, "newData": null, "ipAddress": "::1", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "critical", "details": {"email": "<EMAIL>"}, "createdAt": "2025-06-19T05:46:52.634Z"}, {"userId": null, "action": "LOGIN_FAILED", "entity": "AUTH", "entityId": "<EMAIL>", "oldData": null, "newData": null, "ipAddress": "::1", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "critical", "details": {"email": "<EMAIL>"}, "createdAt": "2025-06-19T05:47:02.799Z"}, {"userId": null, "action": "LOGIN_FAILED", "entity": "AUTH", "entityId": "<EMAIL>", "oldData": null, "newData": null, "ipAddress": "::1", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "critical", "details": {"email": "<EMAIL>"}, "createdAt": "2025-06-19T05:47:10.250Z"}, {"userId": null, "action": "LOGIN_FAILED", "entity": "AUTH", "entityId": "<EMAIL>", "oldData": null, "newData": null, "ipAddress": "::1", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "critical", "details": {"email": "<EMAIL>"}, "createdAt": "2025-06-19T05:47:10.905Z"}, {"userId": null, "action": "LOGIN_FAILED", "entity": "AUTH", "entityId": "<EMAIL>", "oldData": null, "newData": null, "ipAddress": "::1", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "level": "critical", "details": {"email": "<EMAIL>"}, "createdAt": "2025-06-19T05:47:22.657Z"}]